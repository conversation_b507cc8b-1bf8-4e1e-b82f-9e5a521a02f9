<!-- Sidebar -->
<div class="app-menu">
    <div class="navbar-vertical navbar nav-dashboard">
        <div class="h-100" data-simplebar>
            <!-- Brand logo -->
            <a class="navbar-brand d-flex align-items-center" href="<?php echo e(route('dashboard')); ?>" style="padding: 1rem;" title="<?php echo e(config('app.name')); ?>">
                <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" class="me-2" style="width: 35px; height: 35px; object-fit: contain;">
                <span class="fw-bold text-dark" style="font-size: 1.2rem; letter-spacing: 1px;"><?php echo e(app_initials()); ?></span>
            </a>
            
            <!-- Navbar nav -->
            <ul class="navbar-nav flex-column" id="sideNavbar">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link has-arrow <?php echo e(request()->routeIs('dashboard*') ? 'active' : ''); ?>" 
                       href="#!" 
                       data-bs-toggle="collapse" 
                       data-bs-target="#navDashboard" 
                       aria-expanded="<?php echo e(request()->routeIs('dashboard*') ? 'true' : 'false'); ?>" 
                       aria-controls="navDashboard">
                        <i data-feather="home" class="nav-icon me-2 icon-xxs"></i>
                        Dashboard
                    </a>
                    <div id="navDashboard" class="collapse <?php echo e(request()->routeIs('dashboard*') ? 'show' : ''); ?>" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                                    Analytics Overview
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('dashboard.statistics') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard.statistics')); ?>">
                                    Crime Statistics
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('dashboard.quick-actions') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard.quick-actions')); ?>">
                                    Quick Actions
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Criminal Profiles -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('criminals*') ? 'active' : ''); ?>" href="<?php echo e(route('criminals.index')); ?>">
                        <i data-feather="users" class="nav-icon me-2 icon-xxs"></i>
                        Criminal Profiles
                    </a>
                </li>

                <!-- Case Management -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('cases*') ? 'active' : ''); ?>" href="<?php echo e(route('cases.index')); ?>">
                        <i data-feather="folder" class="nav-icon me-2 icon-xxs"></i>
                        Case Management
                    </a>
                </li>

                <!-- Evidence Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow <?php echo e(request()->routeIs('evidence*') ? 'active' : ''); ?>"
                       href="#!"
                       data-bs-toggle="collapse"
                       data-bs-target="#navEvidence"
                       aria-expanded="<?php echo e(request()->routeIs('evidence*') ? 'true' : 'false'); ?>"
                       aria-controls="navEvidence">
                        <i data-feather="archive" class="nav-icon me-2 icon-xxs"></i>
                        Evidence Management
                    </a>
                    <div id="navEvidence" class="collapse <?php echo e(request()->routeIs('evidence*') ? 'show' : ''); ?>" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('evidence.index') ? 'active' : ''); ?>" href="<?php echo e(route('evidence.index')); ?>">
                                    Evidence List
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('evidence.custody') ? 'active' : ''); ?>" href="<?php echo e(route('evidence.custody')); ?>">
                                    Chain of Custody
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('evidence.forensic') ? 'active' : ''); ?>" href="<?php echo e(route('evidence.forensic')); ?>">
                                    Forensic Analysis
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Court Cases -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('court*') ? 'active' : ''); ?>" href="<?php echo e(route('court.hearings')); ?>">
                        <i data-feather="briefcase" class="nav-icon me-2 icon-xxs"></i>
                        Court Cases
                    </a>
                </li>

                <!-- Documents -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('documents*') ? 'active' : ''); ?>" href="<?php echo e(route('documents.library')); ?>">
                        <i data-feather="file-text" class="nav-icon me-2 icon-xxs"></i>
                        Documents
                    </a>
                </li>

                <!-- Reports -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('reports*') ? 'active' : ''); ?>" href="<?php echo e(route('reports.crime')); ?>">
                        <i data-feather="bar-chart-2" class="nav-icon me-2 icon-xxs"></i>
                        Reports
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-item">
                    <div class="navbar-heading">System</div>
                </li>

                <!-- User Management -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('users*') ? 'active' : ''); ?>" href="<?php echo e(route('users.index')); ?>">
                        <i data-feather="users" class="nav-icon me-2 icon-xxs"></i>
                        User Management
                    </a>
                </li>

                <!-- Role Management -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('roles*') ? 'active' : ''); ?>" href="<?php echo e(route('roles.index')); ?>">
                        <i data-feather="shield" class="nav-icon me-2 icon-xxs"></i>
                        Role Management
                    </a>
                </li>

                <!-- Backup Management -->
                <li class="nav-item">
                    <a class="nav-link has-arrow <?php echo e(request()->routeIs('backups*') ? 'active' : ''); ?>"
                       href="#!"
                       data-bs-toggle="collapse"
                       data-bs-target="#navBackups"
                       aria-expanded="<?php echo e(request()->routeIs('backups*') ? 'true' : 'false'); ?>"
                       aria-controls="navBackups">
                        <i data-feather="hard-drive" class="nav-icon me-2 icon-xxs"></i>
                        Backup Management
                    </a>
                    <div id="navBackups" class="collapse <?php echo e(request()->routeIs('backups*') ? 'show' : ''); ?>" data-bs-parent="#sideNavbar">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('backups.index') ? 'active' : ''); ?>" href="<?php echo e(route('backups.index')); ?>">
                                    All Backups
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('backups.create') ? 'active' : ''); ?>" href="<?php echo e(route('backups.create')); ?>">
                                    Create Backup
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('backups.schedule') ? 'active' : ''); ?>" href="<?php echo e(route('backups.schedule')); ?>">
                                    Backup Schedule
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('backups.restore') ? 'active' : ''); ?>" href="<?php echo e(route('backups.restore')); ?>">
                                    Restore Backup
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Settings -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('settings*') ? 'active' : ''); ?>" href="<?php echo e(route('settings.index')); ?>">
                        <i data-feather="settings" class="nav-icon me-2 icon-xxs"></i>
                        Settings
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/components/sidebar.blade.php ENDPATH**/ ?>