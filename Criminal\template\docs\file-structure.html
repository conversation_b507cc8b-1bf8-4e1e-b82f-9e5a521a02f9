<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from dashui.codescandy.com/dashuipro/docs/file-structure.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:56 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

  <title>File Structure  | Dash UI - Bootstrap 5 Admin Dashboard Template</title>
</head>

<body>
  <!-- Main wrapper -->
  <main id="main-wrapper" class="main-wrapper">
    <!-- navbar vertical -->
    <div class="app-menu">
    <!-- Sidebar -->

<div class="navbar-vertical navbar nav-dashboard">
	<div class="h-100" data-simplebar>
		<!-- Brand logo -->
		<a class="navbar-brand" href="../index.html">
			<img src="../assets/images/brand/logo/logo-2.svg" alt="dash ui - bootstrap 5 admin dashboard template" />
		</a>
		<!-- Navbar nav -->
		<ul class="navbar-nav flex-column" id="sideNavbar">
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navDashboard"
					aria-expanded="false"
					aria-controls="navDashboard"
				>
					<i data-feather="home" class="nav-icon me-2 icon-xxs"></i>
					Dashboard
				</a>

				<div id="navDashboard" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../pages/dashboard-analytics.html">Analytics</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Project</a>
						</li>

						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/dashboard-ecommerce.html">Ecommerce</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/dashboard-crm.html">CRM</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/dashboard-finance.html">Finance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/dashboard-blog.html">Blog</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Apps</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="../pages/calendar.html">Calendar</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="../pages/chat-app.html">
					<i data-feather="message-square" class="nav-icon me-2 icon-xxs"></i>
					Chat
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navecommerce"
					aria-expanded="false"
					aria-controls="navecommerce"
				>
					<i data-feather="shopping-cart" class="nav-icon me-2 icon-xxs"></i>
					Ecommerce
				</a>

				<div id="navecommerce" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-products.html">Products</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-products-details.html">Product Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-product-edit.html">Add Product</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-order-list.html">Orders</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-order-detail.html">Orders Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-cart.html">Shopping cart</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-checkout.html">Checkout</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-customer.html">Customer</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/ecommerce-seller.html">Seller</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navEmail" aria-expanded="false" aria-controls="navEmail">
					<i data-feather="mail" class="nav-icon me-2 icon-xxs"></i>
					Email
				</a>

				<div id="navEmail" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/mail.html">Inbox</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/mail-details.html">Details</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/mail-draft.html">Draft</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navKanban"
					aria-expanded="false"
					aria-controls="navKanban"
				>
					<i data-feather="layout" class="nav-icon me-2 icon-xxs"></i>
					Kanban
				</a>

				<div id="navKanban" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/task-kanban-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/task-kanban-list.html">List</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navProject" aria-expanded="false" aria-controls="navProject">
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Project
				</a>
				<div id="navProject" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../pages/project-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/project-list.html">List</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navprojectSingle"
								aria-expanded="false"
								aria-controls="navprojectSingle"
							>
								Single
							</a>
							<div id="navprojectSingle" class="collapse " data-bs-parent="#navProject">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="../pages/project-overview.html">Overview</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="../pages/project-task.html">Task</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="../pages/project-budget.html">Budget</a>
									</li>

									<li class="nav-item">
										<a class="nav-link " href="../pages/project-files.html">Files</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="../pages/project-team.html">Team</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/add-project.html">Create Project</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a class="nav-link " href="../pages/apps-file-manager.html">
					<i data-feather="folder-plus" class="nav-icon me-2 icon-xxs"></i>
					File Manager
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navCRM" aria-expanded="false" aria-controls="navCRM">
					<i data-feather="pie-chart" class="nav-icon me-2 icon-xxs"></i>

					CRM
				</a>

				<div id="navCRM" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/crm-contacts.html">Contacts</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/crm-company.html">Company</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/deals.html">
								Deals
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/deals-single.html">
								Deals Single
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navinvoice"
					aria-expanded="false"
					aria-controls="navinvoice"
				>
					<i data-feather="clipboard" class="nav-icon me-2 icon-xxs"></i>
					Invoice
				</a>

				<div id="navinvoice" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/invoice-list.html">List</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/invoice-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/invoice-generator.html">Invoice Generator</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navprofilePages"
					aria-expanded="false"
					aria-controls="navprofilePages"
				>
					<i data-feather="user" class="nav-icon me-2 icon-xxs"></i>
					Profile
				</a>
				<div id="navprofilePages" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-overview.html">Overview</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-project.html">Project</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-files.html">Files</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-team.html">Team</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-followers.html">Followers</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-activity.html">Activity</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/profile-settings.html">Settings</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navblog" aria-expanded="false" aria-controls="navblog">
					<i data-feather="edit" class="nav-icon me-2 icon-xxs"></i>
					Blog
				</a>

				<div id="navblog" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/blog-author.html">Author</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/blog-author-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="../pages/create-blog-post.html">Create Post</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Layouts & Pages</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navlayoutPage"
					aria-expanded="false"
					aria-controls="navlayoutPage"
				>
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Pages
				</a>
				<div id="navlayoutPage" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../pages/starter.html">Starter</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="../pages/pricing.html">Pricing</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/maintenance.html">Maintenance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/404-error.html">404 Error</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navAuthentication"
					aria-expanded="false"
					aria-controls="navAuthentication"
				>
					<i data-feather="lock" class="nav-icon me-2 icon-xxs"></i>
					Authentication
				</a>
				<div id="navAuthentication" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../pages/sign-in.html">Sign In</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/sign-up.html">Sign Up</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/forget-password.html">Forget Password</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navLayouts"
					aria-expanded="false"
					aria-controls="navLayouts"
				>
					<i data-feather="sidebar" class="nav-icon me-2 icon-xxs"></i>
					Layouts
				</a>
				<div id="navLayouts" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Default</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="../horizontal/index.html">Horizontal</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">UI Components</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navComponents"
					aria-expanded="false"
					aria-controls="navComponents"
				>
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Components
				</a>
				<div id="navComponents" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a href="../pages/components/accordions.html" class="nav-link ">Accordions</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../pages/components/alerts.html">Alert</a>
						</li>

						<li class="nav-item">
							<a href="../pages/components/badge.html" class="nav-link ">Badge</a>
						</li>

						<li class="nav-item">
							<a href="../pages/components/breadcrumb.html" class="nav-link ">Breadcrumb</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/buttons.html" class="nav-link ">Buttons</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/button-group.html" class="nav-link ">Button group</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/card.html" class="nav-link ">Card</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/carousel.html" class="nav-link ">Carousel</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/close-button.html" class="nav-link ">Close Button</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/collapse.html" class="nav-link ">Collapse</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/dropdowns.html" class="nav-link ">Dropdowns</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/forms.html" class="nav-link ">Forms</a>
						</li>

						<li class="nav-item">
							<a href="../pages/components/list-group.html" class="nav-link ">List group</a>
						</li>

						<li class="nav-item">
							<a href="../pages/components/modal.html" class="nav-link ">Modal</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/navs-tabs.html" class="nav-link ">Navs and tabs</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/navbar.html" class="nav-link ">Navbar</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/offcanvas.html" class="nav-link ">Offcanvas</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/pagination.html" class="nav-link ">Pagination</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/placeholders.html" class="nav-link ">Placeholders</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/popovers.html" class="nav-link ">Popovers</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/progress.html" class="nav-link ">Progress</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/scrollspy.html" class="nav-link ">Scrollspy</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/spinners.html" class="nav-link ">Spinners</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/tables.html" class="nav-link ">Tables</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/toasts.html" class="nav-link ">Toasts</a>
						</li>
						<li class="nav-item">
							<a href="../pages/components/tooltips.html" class="nav-link ">Tooltips</a>
						</li>
					</ul>
				</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navMenuLevel"
					aria-expanded="false"
					aria-controls="navMenuLevel"
				>
					<i data-feather="corner-left-down" class="nav-icon me-2 icon-xxs"></i>
					Menu Level
				</a>
				<div id="navMenuLevel" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a
								class="nav-link has-arrow "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelSecond"
								aria-expanded="false"
								aria-controls="navMenuLevelSecond"
							>
								Two Level
							</a>
							<div id="navMenuLevelSecond" class="collapse" data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 1</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 2</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a
								class="nav-link has-arrow  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelThree"
								aria-expanded="false"
								aria-controls="navMenuLevelThree"
							>
								Three Level
							</a>
							<div id="navMenuLevelThree" class="collapse " data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a
											class="nav-link  collapsed "
											href="#!"
											data-bs-toggle="collapse"
											data-bs-target="#navMenuLevelThreeOne"
											aria-expanded="false"
											aria-controls="navMenuLevelThreeOne"
										>
											NavItem 1
										</a>
										<div id="navMenuLevelThreeOne" class="collapse collapse " data-bs-parent="#navMenuLevelThree">
											<ul class="nav flex-column">
												<li class="nav-item">
													<a class="nav-link " href="#!">NavChild Item 1</a>
												</li>
											</ul>
										</div>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">Nav Item 2</a>
									</li>
								</ul>
							</div>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Documentation</div>
			</li>
			<li class="nav-item">
				<a class="nav-link has-arrow " href="#!" data-bs-toggle="collapse" data-bs-target="#navDocs" aria-expanded="false" aria-controls="navDocs">
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Docs
				</a>
				<div id="navDocs" class="collapse  show " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item"><a href="index.html" class="nav-link ">Introduction</a></li>
						<li class="nav-item"><a href="environment-setup.html" class="nav-link ">Environment setup</a></li>
						<li class="nav-item"><a href="working-with-gulp.html" class="nav-link ">Working with Gulp</a></li>
						<li class="nav-item"><a href="compiled-files.html" class="nav-link ">Compiled Files</a></li>
						<li class="nav-item"><a href="file-structure.html" class="nav-link  active ">File Structure</a></li>
						<li class="nav-item"><a href="resources-assets.html" class="nav-link ">Resources & assets</a></li>
						<li class="nav-item"><a href="changelog.html" class="nav-link ">Changelog</a></li>
					</ul>
				</div>
			</li>
		</ul>
		<div class="card bg-light shadow-none text-center mx-4 my-8">
			<div class="card-body py-6">
				<img src="../assets/images/background/giftbox.png" alt="dash ui - admin dashboard template" />
				<div class="mt-4">
					<h5>Unlimited Access</h5>
					<p class="fs-6 mb-4">Upgrade your plan from a Free trial, to select Business Plan. Start Now</p>
					<a href="#" class="btn btn-secondary btn-sm">Upgrade Now</a>
				</div>
			</div>
		</div>
	</div>
</div>

  </div>
    <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

    <!-- Wrapper  -->
 <!-- Page Content -->
 <div id="app-content">
     
    
        
  <!-- Container fluid -->
  <div class="app-content-area">

  
      <div class="container-fluid">
        <div class="row">
          <div class="offset-xxl-1 col-xxl-8 offset-xl-1 col-xl-7 col-md-12 col-sm-12 col-12">
            <!-- Content -->
            <div class="docs-content mx-xxl-9">
              <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                  <div class="mb-8" id="intro">
                    <h1 class=" h2">
               File Structure
                    </h1>
                    <p class="mb-6 lead text-muted">
                        This section will show you how to keep your files organized. Our theme
                        file structure that looks like this.
                    </p>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12 col-12">
                  <div class="mb-8">
                    <h2 class="h3">Theme folder and file structure</h2>
                    <p>
                        As mentioned previously, the default Dash UI themes are some of the best examples of good
                        theme development.
                    </p>



          </div>
           <div>
            <h4 class="mb-3">
              For instance, here is how the Dash UI Theme organizes its file structure:
          </h4>
            <ul class="list-group list-group-flush">
                <li class="list-group-item px-0   ">
                    <div class="row">
                        <div class="col-auto">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder-fill text-body" viewBox="0 0 16 16">
                                    <path d="M9.828 3h3.982a2 2 0 0 1 1.992 2.181l-.637 7A2 2 0 0 1 13.174 14H2.825a2 2 0 0 1-1.991-1.819l-.637-7a1.99 1.99 0 0 1 .342-1.31L.5 3a2 2 0 0 1 2-2h3.672a2 2 0 0 1 1.414.586l.828.828A2 2 0 0 0 9.828 3zm-8.322.12C1.72 3.042 1.95 3 2.19 3h5.396l-.707-.707A1 1 0 0 0 6.172 2H2.5a1 1 0 0 0-1 .981l.006.139z"/>
                                  </svg>
                            </div>
                        </div>
                        <div class="col d-flex ms-n3 ">

                          <span class="me-4 text-dark fw-bold">Dash UI</span><p class="mb-0">Folder contains all template source and production files.</p>

                        </div>
                    </div>
                    <ul class="list-group list-group-flush ps-3 mt-3">
                        <li class="list-group-item px-0  ">
                            <div class="row">
                                <div class="col-auto">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                            <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                          </svg>
                                    </div>
                                </div>
                                <div class="col d-flex ms-n3 ">

                                  <span class="me-4 text-dark fw-bold">dist</span><p class="mb-0">Compiled version - Ready to use</p>

                                </div>
                            </div>
                        </li>
                        <li class="list-group-item px-0   ">
                          <div class="row">
                              <div class="col-auto">
                                  <div>
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                          <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                        </svg>
                                  </div>
                              </div>
                              <div class="col d-flex ms-n3">

                                <span class="me-4 text-dark fw-bold">node_modules</span> <p class="mb-0 ">Directory where npm installs dependencies.</p>

                              </div>
                          </div>
                      </li>
                      <li class="list-group-item px-0   ">
                        <div class="row">
                            <div class="col-auto">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                        <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                      </svg>
                                </div>
                            </div>
                            <div class="col d-flex ms-n3">

                               <span class="me-4 text-dark fw-bold">src</span> <p class="mb-0 ">This folder holds all template source files and folder. </p>

                            </div>
                        </div>
                        <ul class="list-group list-group-flush ps-3 mt-3">
                          <li class="list-group-item px-0  border-top">
                              <div class="row">
                                  <div class="col-auto">
                                      <div>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                              <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                            </svg>
                                      </div>
                                  </div>
                                  <div class="col ms-n3">

                                      <p class="mb-0 "><span class="me-4 text-dark fw-bold">assets</span></p>

                                  </div>
                              </div>
                              <ul class="list-group list-group-flush ps-3 mt-3">
                                <li class="list-group-item px-0  border-top">
                                    <div class="row">
                                        <div class="col-auto">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                                    <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                                  </svg>
                                            </div>
                                        </div>
                                        <div class="col d-flex ms-n3">

                                          <span class="me-4 text-dark fw-bold">css</span> <p class="mb-0 ">
                                              Compiled CSS</p>

                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item px-0   ">
                                  <div class="row">
                                      <div class="col-auto">
                                          <div>
                                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                                  <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                                </svg>
                                          </div>
                                      </div>
                                      <div class="col d-flex ms-n3">

                                        <span class="me-4 text-dark fw-bold">fonts</span> <p class="mb-0">
                                            All fonts are used in the theme.</p>

                                      </div>
                                  </div>
                              </li>
                              <li class="list-group-item px-0   ">
                                <div class="row">
                                    <div class="col-auto">
                                        <div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                                <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                              </svg>
                                        </div>
                                    </div>
                                    <div class="col d-flex ms-n3">

                                      <span class="me-4 text-dark fw-bold">images</span>  <p class="mb-0">
                                          All the images are used in the theme</p>

                                    </div>
                                </div>
                            </li>
                            <li class="list-group-item px-0   ">
                              <div class="row">
                                  <div class="col-auto">
                                      <div>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                              <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                            </svg>
                                      </div>
                                  </div>
                                  <div class="col d-flex ms-n3">

                                    <span class="me-4 text-dark fw-bold">js</span> <p class="mb-0 ">
                                        All Javascript source files used in theme.</p>

                                  </div>
                              </div>
                          </li>
                          <li class="list-group-item px-0  pb-0">
                            <div class="row">
                                <div class="col-auto">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                            <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                          </svg>
                                    </div>
                                </div>
                                <div class="col d-flex ms-n3">

                                  <span class="me-4 text-dark fw-bold">scss</span> <p class="mb-0 ">
                                    All SASS file and folder included in it</p>

                                </div>
                            </div>
                        </li>






                                </ul>
                          </li>
                          <li class="list-group-item px-0   ">
                            <div class="row">
                                <div class="col-auto">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                            <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                          </svg>
                                    </div>
                                </div>
                                <div class="col d-flex ms-n3 ">

                                  <span class="me-4 text-dark fw-bold">docs</span>   <p class="mb-0 ">A Complete Documentation for theme.</p>

                                </div>
                            </div>
                        </li>
                        <li class="list-group-item px-0   ">
                          <div class="row">
                              <div class="col-auto">
                                  <div>
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                          <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                        </svg>
                                  </div>
                              </div>
                              <div class="col d-flex ms-n3">

                                <span class="me-4 text-dark fw-bold">pages</span><p class="mb-0">All the theme HTML pages with Subfolder. </p>

                              </div>
                          </div>
                      </li>
                      <li class="list-group-item px-0   ">
                        <div class="row">
                            <div class="col-auto">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                        <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                      </svg>
                                </div>
                            </div>
                            <div class="col d-flex ms-n3 ">

                              <span class="me-4 text-dark fw-bold">partials</span>  <p class="mb-0">A specific loop header and footer files for the templating. </p>

                            </div>
                        </div>
                    </li>
                    <li class="list-group-item px-0   ">
                      <div class="row">
                          <div class="col-auto">
                              <div>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                      <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                    </svg>
                              </div>
                          </div>
                          <div class="col  d-flex ms-n3">

                            <span class="me-4 text-dark fw-bold">index.html</span><p class="mb-0">Index file is start file run when the gulp </p>

                          </div>
                      </div>
                  </li>
                  <li class="list-group-item px-0   ">
                    <div class="row">
                        <div class="col-auto">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder2 text-body" viewBox="0 0 16 16">
                                    <path d="M1 3.5A1.5 1.5 0 0 1 2.5 2h2.764c.958 0 1.76.56 2.311 1.184C7.985 3.648 8.48 4 9 4h4.5A1.5 1.5 0 0 1 15 5.5v7a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 1 12.5v-9zM2.5 3a.5.5 0 0 0-.5.5V6h12v-.5a.5.5 0 0 0-.5-.5H9c-.964 0-1.71-.629-2.174-1.154C6.374 3.334 5.82 3 5.264 3H2.5zM14 7H2v5.5a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5V7z"/>
                                  </svg>
                            </div>
                        </div>
                        <div class="col d-flex ms-n3 ">

                            <span class="me-4 text-dark fw-bold">gulpfile.js</span><p class="mb-0">Configuration file for Gulp library. It contains all tasks you want to
                              perform with Gulp. Learn more about it from section or <a href="working-with-gulp.html">Working with Gulp</a><a href="https://gulpjs.com/"> official Gulp documentation</a> </p>

                        </div>
                    </div>
                </li>
                          </ul>
                    </li>
                        </ul>
                </li>



            </ul>
           </div>
                </div>
              </div>
            </div>
          </div>
          <div class="
								col-xl-2 col-lg-2 col-md-6 col-sm-12 col-12
								d-none d-xl-block
								position-fixed
								end-0
							">
            <!-- Sidebar nav fixed -->
            <div class="sidebar-nav-fixed">
              <span class="px-4 mb-2 d-block text-uppercase ls-md h3 fs-6">Contents</span>

              <ul class="list-unstyled">
                <li><a href="#intro" class="active">Introduction</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </main>

  <!-- Scripts -->

  <!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

</body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/docs/file-structure.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:56 GMT -->
</html>