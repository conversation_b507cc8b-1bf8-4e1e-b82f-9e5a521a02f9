@extends('layouts.app')

@section('title', 'Role Assignments - ' . config('app.name'))

@section('content')
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Role Assignments</h1>
            <x-breadcrumb :items="[
                ['title' => 'System'],
                ['title' => 'Role Management', 'url' => route('roles.index')],
                ['title' => 'Role Assignments']
            ]" />
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignRoleModal">
                <i data-feather="user-plus" class="icon-xs me-2"></i>
                Assign Role
            </button>
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Roles
            </a>
        </div>
    </div>

    <!-- Assignment Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">54</h4>
                    <p class="mb-0">Total Assignments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success">48</h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-info">8</h4>
                    <p class="mb-0">Available Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning">6</h4>
                    <p class="mb-0">Unassigned Users</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Assignment Matrix -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="mb-0">
                <i data-feather="grid" class="icon-sm me-2"></i>
                Role Assignment Matrix
            </h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Role</th>
                            <th class="text-center">Super Admin</th>
                            <th class="text-center">Admin</th>
                            <th class="text-center">Inspector</th>
                            <th class="text-center">Officer</th>
                            <th class="text-center">Forensic</th>
                            <th class="text-center">Court Liaison</th>
                            <th class="text-center">Viewer</th>
                            <th class="text-center">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Active Users</strong></td>
                            <td class="text-center"><span class="badge bg-danger">2</span></td>
                            <td class="text-center"><span class="badge bg-warning">5</span></td>
                            <td class="text-center"><span class="badge bg-primary">12</span></td>
                            <td class="text-center"><span class="badge bg-success">18</span></td>
                            <td class="text-center"><span class="badge bg-info">6</span></td>
                            <td class="text-center"><span class="badge bg-secondary">3</span></td>
                            <td class="text-center"><span class="badge bg-light text-dark">8</span></td>
                            <td class="text-center"><strong>54</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Permissions</strong></td>
                            <td class="text-center">All</td>
                            <td class="text-center">18</td>
                            <td class="text-center">15</td>
                            <td class="text-center">10</td>
                            <td class="text-center">8</td>
                            <td class="text-center">6</td>
                            <td class="text-center">4</td>
                            <td class="text-center">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- User Role Assignments -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">User Role Assignments</h4>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i data-feather="filter" class="icon-xs me-2"></i>
                            Filter by Role
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('all')">All Roles</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('super_admin')">Super Admin</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('admin')">Administrator</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('inspector')">Inspector</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('officer')">Officer</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('forensic_analyst')">Forensic Analyst</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('court_liaison')">Court Liaison</a></li>
                            <li><a class="dropdown-item" href="#!" onclick="filterByRole('viewer')">Viewer</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-sm btn-outline-info" onclick="exportAssignments()">
                        <i data-feather="download" class="icon-xs me-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>User</th>
                            <th>Current Role</th>
                            <th>Department</th>
                            <th>Assigned Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-danger text-white me-3">SA</div>
                                    <div>
                                        <strong>System Administrator</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-danger">Super Administrator</span></td>
                            <td>System Administration</td>
                            <td>01/01/2024</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><span class="dropdown-item-text text-muted">Cannot modify system admin</span></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-primary text-white me-3">KB</div>
                                    <div>
                                        <strong>Inspector Kondwani Banda</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-primary">Inspector</span></td>
                            <td>Criminal Investigation</td>
                            <td>15/01/2024</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!" onclick="changeRole(1)">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Change Role
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="viewPermissions(1)">
                                            <i data-feather="key" class="icon-xs me-2"></i>View Permissions
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!" onclick="removeRole(1)">
                                            <i data-feather="user-minus" class="icon-xs me-2"></i>Remove Role
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-success text-white me-3">CM</div>
                                    <div>
                                        <strong>Sergeant Chikondi Mwale</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">Officer</span></td>
                            <td>Traffic Police</td>
                            <td>20/02/2024</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!" onclick="changeRole(2)">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Change Role
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="viewPermissions(2)">
                                            <i data-feather="key" class="icon-xs me-2"></i>View Permissions
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!" onclick="removeRole(2)">
                                            <i data-feather="user-minus" class="icon-xs me-2"></i>Remove Role
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-info text-white me-3">TP</div>
                                    <div>
                                        <strong>Constable Thokozani Phiri</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-info">Forensic Analyst</span></td>
                            <td>Forensics</td>
                            <td>10/03/2024</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#!" onclick="changeRole(3)">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Change Role
                                        </a></li>
                                        <li><a class="dropdown-item" href="#!" onclick="viewPermissions(3)">
                                            <i data-feather="key" class="icon-xs me-2"></i>View Permissions
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="#!" onclick="removeRole(3)">
                                            <i data-feather="user-minus" class="icon-xs me-2"></i>Remove Role
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr class="table-warning">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm bg-secondary text-white me-3">LM</div>
                                    <div>
                                        <strong>Constable Lucy Mbewe</strong>
                                        <br><small class="text-muted"><EMAIL></small>
                                        <span class="badge bg-warning ms-2">No Role</span>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-secondary">Unassigned</span></td>
                            <td>Community Policing</td>
                            <td>-</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="assignRole(4)">
                                    <i data-feather="user-plus" class="icon-xs me-2"></i>
                                    Assign Role
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Assign Role Modal -->
    <div class="modal fade" id="assignRoleModal" tabindex="-1" aria-labelledby="assignRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignRoleModalLabel">
                        <i data-feather="user-plus" class="icon-sm me-2"></i>
                        Assign Role to User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignRoleForm">
                        <div class="mb-3">
                            <label for="select_user" class="form-label">Select User</label>
                            <select class="form-select" id="select_user">
                                <option value="">Choose a user...</option>
                                <option value="4">Constable Lucy Mbewe (Unassigned)</option>
                                <option value="5">Officer James Banda (Unassigned)</option>
                                <option value="6">Constable Mary Phiri (Unassigned)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="select_role" class="form-label">Select Role</label>
                            <select class="form-select" id="select_role">
                                <option value="">Choose a role...</option>
                                <option value="admin">Administrator</option>
                                <option value="inspector">Inspector</option>
                                <option value="officer">Officer</option>
                                <option value="forensic_analyst">Forensic Analyst</option>
                                <option value="court_liaison">Court Liaison</option>
                                <option value="viewer">Viewer</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="assignment_reason" class="form-label">Assignment Reason</label>
                            <textarea class="form-control" id="assignment_reason" rows="3" 
                                      placeholder="Reason for this role assignment..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRoleAssignment()">
                        <i data-feather="save" class="icon-xs me-2"></i>
                        Assign Role
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});

function filterByRole(role) {
    alert(`Filtering assignments by role: ${role}`);
}

function exportAssignments() {
    alert('Exporting role assignments...');
}

function changeRole(userId) {
    alert(`Changing role for user ${userId}`);
}

function viewPermissions(userId) {
    alert(`Viewing permissions for user ${userId}`);
}

function removeRole(userId) {
    if (confirm('Are you sure you want to remove this role assignment?')) {
        alert(`Removing role for user ${userId}`);
    }
}

function assignRole(userId) {
    // Pre-select the user in the modal
    document.getElementById('select_user').value = userId;
    
    // Show the modal
    new bootstrap.Modal(document.getElementById('assignRoleModal')).show();
}

function submitRoleAssignment() {
    const user = document.getElementById('select_user').value;
    const role = document.getElementById('select_role').value;
    const reason = document.getElementById('assignment_reason').value;
    
    if (!user || !role) {
        alert('Please select both a user and a role.');
        return;
    }
    
    // In a real application, this would submit to the server
    alert(`Assigning role ${role} to user ${user}. Reason: ${reason}`);
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('assignRoleModal')).hide();
    
    // Reset form
    document.getElementById('assignRoleForm').reset();
}
</script>
@endpush
