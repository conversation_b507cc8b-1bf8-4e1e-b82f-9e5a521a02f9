<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CriminalCase;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CaseController extends Controller
{
    /**
     * Display a listing of the cases.
     */
    public function index(Request $request)
    {
        $query = CriminalCase::with(['reportingOfficer', 'investigatingOfficer', 'criminals']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->searchByTitle($search);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->get('status'));
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->byType($request->get('type'));
        }

        // Filter by district
        if ($request->filled('district')) {
            $query->byDistrict($request->get('district'));
        }

        // Filter by investigating officer
        if ($request->filled('officer')) {
            $query->byOfficer($request->get('officer'));
        }

        $cases = $query->latest()->paginate(10);

        // Append query parameters to pagination links
        $cases->appends($request->query());

        return view('cases.index', compact('cases'));
    }

    /**
     * Show the form for creating a new case.
     */
    public function create()
    {
        $officers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['Police Officer', 'Detective', 'Inspector', 'Supervisor']);
        })->get();

        return view('cases.create', compact('officers'));
    }

    /**
     * Store a newly created case in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:300',
            'description' => 'required|string',
            'type' => 'required|in:Theft,Robbery,Burglary,Assault,Murder,Rape,Drug Offense,Fraud,Embezzlement,Domestic Violence,Traffic Offense,Cybercrime,Corruption,Other',
            'severity' => 'required|in:Minor,Moderate,Serious,Critical',
            'priority' => 'required|in:Low,Medium,High,Urgent',
            'incident_location' => 'required|string',
            'district' => 'required|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'incident_date' => 'required|date|before_or_equal:today',
            'reported_date' => 'required|date|before_or_equal:today|after_or_equal:incident_date',
            'investigating_officer_id' => 'nullable|exists:users,id',
            'supervisor_id' => 'nullable|exists:users,id',
            'complainant_name' => 'required|string|max:200',
            'complainant_phone' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'complainant_email' => 'nullable|email|max:100',
            'complainant_address' => 'nullable|string',
            'complainant_id_number' => 'nullable|string|max:20',
            'estimated_loss' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:10',
            'modus_operandi' => 'nullable|string',
            'evidence_summary' => 'nullable|string',
            'witness_summary' => 'nullable|string',
            'investigation_notes' => 'nullable|string',
            'media_attention' => 'boolean',
            'high_profile' => 'boolean',
        ], [
            'complainant_phone.regex' => 'Phone number must be in the format +265999123456 (Malawi format)',
            'incident_date.before_or_equal' => 'Incident date cannot be in the future',
            'reported_date.after_or_equal' => 'Reported date cannot be before incident date',
        ]);

        // Set default values
        $validated['reporting_officer_id'] = Auth::id();
        $validated['status'] = 'Reported';
        $validated['progress_percentage'] = 0;
        $validated['court_case_filed'] = false;
        $validated['currency'] = $validated['currency'] ?? 'MWK';

        $case = CriminalCase::create($validated);

        return redirect()->route('cases.show', $case)
                        ->with('success', 'Case created successfully.');
    }

    /**
     * Display case search page.
     */
    public function search()
    {
        return view('cases.search');
    }

    /**
     * Display case reports page.
     */
    public function reports()
    {
        return view('cases.reports');
    }

    /**
     * Display the specified case.
     */
    public function show(CriminalCase $case)
    {
        $case->load(['reportingOfficer', 'investigatingOfficer', 'supervisor', 'criminals', 'evidence']);
        return view('cases.show', compact('case'));
    }

    /**
     * Show the form for editing the specified case.
     */
    public function edit(CriminalCase $case)
    {
        $officers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['Police Officer', 'Detective', 'Inspector', 'Supervisor']);
        })->get();

        return view('cases.edit', compact('case', 'officers'));
    }

    /**
     * Update the specified case in storage.
     */
    public function update(Request $request, CriminalCase $case)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:300',
            'description' => 'required|string',
            'type' => 'required|in:Theft,Robbery,Burglary,Assault,Murder,Rape,Drug Offense,Fraud,Embezzlement,Domestic Violence,Traffic Offense,Cybercrime,Corruption,Other',
            'severity' => 'required|in:Minor,Moderate,Serious,Critical',
            'priority' => 'required|in:Low,Medium,High,Urgent',
            'incident_location' => 'required|string',
            'district' => 'required|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'incident_date' => 'required|date|before_or_equal:today',
            'reported_date' => 'required|date|before_or_equal:today|after_or_equal:incident_date',
            'investigating_officer_id' => 'nullable|exists:users,id',
            'supervisor_id' => 'nullable|exists:users,id',
            'complainant_name' => 'required|string|max:200',
            'complainant_phone' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'complainant_email' => 'nullable|email|max:100',
            'complainant_address' => 'nullable|string',
            'complainant_id_number' => 'nullable|string|max:20',
            'status' => 'required|in:Reported,Under Investigation,Pending Evidence,Pending Arrest,Suspect Arrested,Case Closed,Transferred,Cold Case',
            'progress_percentage' => 'required|integer|min:0|max:100',
            'status_notes' => 'nullable|string',
            'court_case_filed' => 'boolean',
            'court_case_number' => 'nullable|string|max:50',
            'court_name' => 'nullable|string|max:200',
            'court_date' => 'nullable|date|after:today',
            'estimated_loss' => 'nullable|numeric|min:0',
            'recovered_amount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:10',
            'modus_operandi' => 'nullable|string',
            'evidence_summary' => 'nullable|string',
            'witness_summary' => 'nullable|string',
            'investigation_notes' => 'nullable|string',
            'media_attention' => 'boolean',
            'high_profile' => 'boolean',
            'closure_reason' => 'nullable|in:Solved,Insufficient Evidence,Suspect Not Found,Withdrawn by Complainant,Transferred,Other',
            'closure_notes' => 'nullable|string',
        ], [
            'complainant_phone.regex' => 'Phone number must be in the format +265999123456 (Malawi format)',
            'incident_date.before_or_equal' => 'Incident date cannot be in the future',
            'reported_date.after_or_equal' => 'Reported date cannot be before incident date',
        ]);

        // Set closure date if case is being closed
        if ($validated['status'] === 'Case Closed' && !$case->closed_date) {
            $validated['closed_date'] = now()->toDateString();
        } elseif ($validated['status'] !== 'Case Closed') {
            $validated['closed_date'] = null;
        }

        $case->update($validated);

        return redirect()->route('cases.show', $case)
                        ->with('success', 'Case updated successfully.');
    }

    /**
     * Remove the specified case from storage.
     */
    public function destroy(CriminalCase $case)
    {
        $case->delete();

        return redirect()->route('cases.index')
                        ->with('success', 'Case deleted successfully.');
    }
}
