@extends('layouts.app')

@section('title', 'Case Management')

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Cases</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Case Management</h1>
        <a href="{{ route('cases.create') }}" class="btn btn-primary">
            <i data-feather="plus" class="icon-sm me-2"></i>
            Add New Case
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="text-white">{{ $cases->total() }}</h4>
                    <p class="mb-0">Total Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="text-white">{{ $cases->where('status', 'Under Investigation')->count() }}</h4>
                    <p class="mb-0">Under Investigation</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="text-white">{{ $cases->where('status', 'Case Closed')->count() }}</h4>
                    <p class="mb-0">Closed Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 class="text-white">{{ $cases->where('priority', 'High')->count() + $cases->where('priority', 'Urgent')->count() }}</h4>
                    <p class="mb-0">High Priority</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="true">
                    <i data-feather="chevron-up" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="filtersCollapse">
            <div class="card-body">
                <form method="GET" action="{{ route('cases.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" 
                               placeholder="Case number, title, complainant...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="Reported" {{ request('status') == 'Reported' ? 'selected' : '' }}>Reported</option>
                            <option value="Under Investigation" {{ request('status') == 'Under Investigation' ? 'selected' : '' }}>Under Investigation</option>
                            <option value="Pending Evidence" {{ request('status') == 'Pending Evidence' ? 'selected' : '' }}>Pending Evidence</option>
                            <option value="Pending Arrest" {{ request('status') == 'Pending Arrest' ? 'selected' : '' }}>Pending Arrest</option>
                            <option value="Suspect Arrested" {{ request('status') == 'Suspect Arrested' ? 'selected' : '' }}>Suspect Arrested</option>
                            <option value="Case Closed" {{ request('status') == 'Case Closed' ? 'selected' : '' }}>Case Closed</option>
                            <option value="Transferred" {{ request('status') == 'Transferred' ? 'selected' : '' }}>Transferred</option>
                            <option value="Cold Case" {{ request('status') == 'Cold Case' ? 'selected' : '' }}>Cold Case</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <option value="Low" {{ request('priority') == 'Low' ? 'selected' : '' }}>Low</option>
                            <option value="Medium" {{ request('priority') == 'Medium' ? 'selected' : '' }}>Medium</option>
                            <option value="High" {{ request('priority') == 'High' ? 'selected' : '' }}>High</option>
                            <option value="Urgent" {{ request('priority') == 'Urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="Theft" {{ request('type') == 'Theft' ? 'selected' : '' }}>Theft</option>
                            <option value="Robbery" {{ request('type') == 'Robbery' ? 'selected' : '' }}>Robbery</option>
                            <option value="Burglary" {{ request('type') == 'Burglary' ? 'selected' : '' }}>Burglary</option>
                            <option value="Assault" {{ request('type') == 'Assault' ? 'selected' : '' }}>Assault</option>
                            <option value="Murder" {{ request('type') == 'Murder' ? 'selected' : '' }}>Murder</option>
                            <option value="Rape" {{ request('type') == 'Rape' ? 'selected' : '' }}>Rape</option>
                            <option value="Drug Offense" {{ request('type') == 'Drug Offense' ? 'selected' : '' }}>Drug Offense</option>
                            <option value="Fraud" {{ request('type') == 'Fraud' ? 'selected' : '' }}>Fraud</option>
                            <option value="Embezzlement" {{ request('type') == 'Embezzlement' ? 'selected' : '' }}>Embezzlement</option>
                            <option value="Domestic Violence" {{ request('type') == 'Domestic Violence' ? 'selected' : '' }}>Domestic Violence</option>
                            <option value="Traffic Offense" {{ request('type') == 'Traffic Offense' ? 'selected' : '' }}>Traffic Offense</option>
                            <option value="Cybercrime" {{ request('type') == 'Cybercrime' ? 'selected' : '' }}>Cybercrime</option>
                            <option value="Corruption" {{ request('type') == 'Corruption' ? 'selected' : '' }}>Corruption</option>
                            <option value="Other" {{ request('type') == 'Other' ? 'selected' : '' }}>Other</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="district" class="form-label">District</label>
                        <input type="text" class="form-control" id="district" name="district" 
                               value="{{ request('district') }}" 
                               placeholder="District">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs"></i>
                            </button>
                        </div>
                    </div>
                    @if(request()->hasAny(['search', 'status', 'priority', 'type', 'district', 'officer']))
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ route('cases.index') }}" class="btn btn-outline-secondary">
                                <i data-feather="x" class="icon-xs"></i>
                            </a>
                        </div>
                    </div>
                    @endif
                </form>
            </div>
        </div>
    </div>

    <!-- Cases Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Cases List</h5>
        </div>
        <div class="card-body p-0">
            @if($cases->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Case Number</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Investigating Officer</th>
                            <th>Reported Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($cases as $case)
                        <tr>
                            <td>
                                <a href="{{ route('cases.show', $case) }}" class="text-decoration-none">
                                    {{ $case->case_number }}
                                </a>
                            </td>
                            <td>
                                <div class="fw-medium">{{ Str::limit($case->title, 40) }}</div>
                                <small class="text-muted">{{ $case->complainant_name }}</small>
                            </td>
                            <td>{{ $case->type }}</td>
                            <td>
                                <span class="badge {{ $case->priority_badge_class }}">
                                    {{ $case->priority }}
                                </span>
                            </td>
                            <td>
                                <span class="badge {{ $case->status_badge_class }}">
                                    {{ $case->status }}
                                </span>
                            </td>
                            <td>
                                {{ $case->investigatingOfficer->name ?? 'Not Assigned' }}
                            </td>
                            <td>{{ $case->reported_date->format('M d, Y') }}</td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('cases.show', $case) }}">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('cases.edit', $case) }}">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ route('cases.destroy', $case) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger" 
                                                        onclick="return confirm('Are you sure you want to delete this case?')">
                                                    <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="text-center py-5">
                <i data-feather="folder" class="icon-lg text-muted mb-3"></i>
                <h5 class="text-muted">No cases found</h5>
                <p class="text-muted">No cases match your current filters.</p>
                <a href="{{ route('cases.create') }}" class="btn btn-primary">
                    <i data-feather="plus" class="icon-sm me-2"></i>
                    Add First Case
                </a>
            </div>
            @endif
        </div>
        @if($cases->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing {{ $cases->firstItem() }} to {{ $cases->lastItem() }} of {{ $cases->total() }} results
                </div>
                {{ $cases->links('pagination::bootstrap-4') }}
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Initialize Feather icons
    feather.replace();
</script>
@endpush
