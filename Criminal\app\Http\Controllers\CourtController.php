<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Carbon\Carbon;

class CourtController extends Controller
{
    /**
     * Display court hearings
     */
    public function hearings(Request $request)
    {
        // Mock data for court hearings
        $hearings = collect([
            [
                'id' => 1,
                'case_number' => 'CR/2024/001',
                'case_title' => 'State vs. <PERSON>',
                'hearing_date' => Carbon::now()->addDays(3),
                'hearing_time' => '09:00',
                'court_room' => 'Court Room 1',
                'judge' => 'Hon. Justice <PERSON>',
                'case_type' => 'Criminal',
                'status' => 'scheduled',
                'prosecutor' => 'State Prosecutor <PERSON>',
                'defendant' => '<PERSON>',
                'charges' => 'Theft, Burglary'
            ],
            [
                'id' => 2,
                'case_number' => 'CR/2024/002',
                'case_title' => 'State vs. Mary Tembo',
                'hearing_date' => Carbon::now()->addDays(5),
                'hearing_time' => '14:00',
                'court_room' => 'Court Room 2',
                'judge' => 'Hon. <PERSON>',
                'case_type' => 'Criminal',
                'status' => 'scheduled',
                'prosecutor' => 'State Prosecutor B. Chikwawa',
                'defendant' => 'Mary Tembo',
                'charges' => 'Fraud'
            ],
            [
                'id' => 3,
                'case_number' => 'CR/2024/003',
                'case_title' => 'State vs. Peter Mbewe',
                'hearing_date' => Carbon::now()->subDays(2),
                'hearing_time' => '10:30',
                'court_room' => 'Court Room 1',
                'judge' => 'Hon. Justice M. Phiri',
                'case_type' => 'Criminal',
                'status' => 'completed',
                'prosecutor' => 'State Prosecutor C. Gondwe',
                'defendant' => 'Peter Mbewe',
                'charges' => 'Assault'
            ],
            [
                'id' => 4,
                'case_number' => 'CR/2024/004',
                'case_title' => 'State vs. Grace Kachingwe',
                'hearing_date' => Carbon::now()->addDays(1),
                'hearing_time' => '11:00',
                'court_room' => 'Court Room 3',
                'judge' => 'Hon. Justice L. Banda',
                'case_type' => 'Criminal',
                'status' => 'scheduled',
                'prosecutor' => 'State Prosecutor D. Msiska',
                'defendant' => 'Grace Kachingwe',
                'charges' => 'Drug Possession'
            ]
        ]);

        // Apply filters
        if ($request->filled('status')) {
            $hearings = $hearings->where('status', $request->status);
        }

        if ($request->filled('court_room')) {
            $hearings = $hearings->where('court_room', $request->court_room);
        }

        if ($request->filled('date')) {
            $filterDate = Carbon::parse($request->date);
            $hearings = $hearings->filter(function ($hearing) use ($filterDate) {
                return $hearing['hearing_date']->isSameDay($filterDate);
            });
        }

        // Simulate pagination
        $perPage = 20;
        $currentPage = $request->get('page', 1);
        $total = $hearings->count();
        $hearingsPaginated = $hearings->forPage($currentPage, $perPage);

        // Create a paginator instance
        $hearings = new \Illuminate\Pagination\LengthAwarePaginator(
            $hearingsPaginated,
            $total,
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        return view('court.hearings', compact('hearings'));
    }

    /**
     * Display court case status
     */
    public function status(Request $request)
    {
        // Mock data for case status
        $cases = collect([
            [
                'id' => 1,
                'case_number' => 'CR/2024/001',
                'case_title' => 'State vs. John Banda',
                'status' => 'In Progress',
                'filed_date' => Carbon::now()->subDays(30),
                'next_hearing' => Carbon::now()->addDays(3),
                'judge' => 'Hon. Justice M. Phiri',
                'prosecutor' => 'State Prosecutor A. Mwale',
                'defendant' => 'John Banda',
                'charges' => 'Theft, Burglary',
                'progress' => 65
            ],
            [
                'id' => 2,
                'case_number' => 'CR/2024/002',
                'case_title' => 'State vs. Mary Tembo',
                'status' => 'Pending',
                'filed_date' => Carbon::now()->subDays(15),
                'next_hearing' => Carbon::now()->addDays(5),
                'judge' => 'Hon. Justice K. Nyirenda',
                'prosecutor' => 'State Prosecutor B. Chikwawa',
                'defendant' => 'Mary Tembo',
                'charges' => 'Fraud',
                'progress' => 25
            ],
            [
                'id' => 3,
                'case_number' => 'CR/2024/003',
                'case_title' => 'State vs. Peter Mbewe',
                'status' => 'Closed',
                'filed_date' => Carbon::now()->subDays(60),
                'next_hearing' => null,
                'judge' => 'Hon. Justice M. Phiri',
                'prosecutor' => 'State Prosecutor C. Gondwe',
                'defendant' => 'Peter Mbewe',
                'charges' => 'Assault',
                'progress' => 100
            ]
        ]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $cases = $cases->filter(function ($case) use ($search) {
                return stripos($case['case_number'], $search) !== false ||
                       stripos($case['case_title'], $search) !== false ||
                       stripos($case['defendant'], $search) !== false;
            });
        }

        // Apply status filter
        if ($request->filled('status_filter')) {
            $cases = $cases->where('status', $request->status_filter);
        }

        return view('court.status', compact('cases'));
    }

    /**
     * Display court calendar
     */
    public function calendar(Request $request)
    {
        // Mock data for calendar events
        $events = collect([
            [
                'id' => 1,
                'title' => 'State vs. John Banda - Hearing',
                'start' => Carbon::now()->addDays(3)->format('Y-m-d'),
                'time' => '09:00',
                'court_room' => 'Court Room 1',
                'case_number' => 'CR/2024/001',
                'type' => 'hearing'
            ],
            [
                'id' => 2,
                'title' => 'State vs. Mary Tembo - Hearing',
                'start' => Carbon::now()->addDays(5)->format('Y-m-d'),
                'time' => '14:00',
                'court_room' => 'Court Room 2',
                'case_number' => 'CR/2024/002',
                'type' => 'hearing'
            ],
            [
                'id' => 3,
                'title' => 'State vs. Grace Kachingwe - Hearing',
                'start' => Carbon::now()->addDays(1)->format('Y-m-d'),
                'time' => '11:00',
                'court_room' => 'Court Room 3',
                'case_number' => 'CR/2024/004',
                'type' => 'hearing'
            ],
            [
                'id' => 4,
                'title' => 'Court Administration Meeting',
                'start' => Carbon::now()->addDays(7)->format('Y-m-d'),
                'time' => '15:00',
                'court_room' => 'Conference Room',
                'case_number' => null,
                'type' => 'meeting'
            ]
        ]);

        return view('court.calendar', compact('events'));
    }

    /**
     * Display court verdicts
     */
    public function verdicts(Request $request)
    {
        // Mock data for verdicts
        $verdicts = collect([
            [
                'id' => 1,
                'case_number' => 'CR/2023/045',
                'case_title' => 'State vs. James Phiri',
                'defendant' => 'James Phiri',
                'charges' => 'Armed Robbery',
                'verdict' => 'Guilty',
                'sentence' => '10 years imprisonment',
                'verdict_date' => Carbon::now()->subDays(5),
                'judge' => 'Hon. Justice M. Phiri',
                'prosecutor' => 'State Prosecutor A. Mwale'
            ],
            [
                'id' => 2,
                'case_number' => 'CR/2023/052',
                'case_title' => 'State vs. Susan Mwale',
                'defendant' => 'Susan Mwale',
                'charges' => 'Embezzlement',
                'verdict' => 'Guilty',
                'sentence' => '5 years imprisonment + K2,000,000 fine',
                'verdict_date' => Carbon::now()->subDays(12),
                'judge' => 'Hon. Justice K. Nyirenda',
                'prosecutor' => 'State Prosecutor B. Chikwawa'
            ],
            [
                'id' => 3,
                'case_number' => 'CR/2023/038',
                'case_title' => 'State vs. Michael Banda',
                'defendant' => 'Michael Banda',
                'charges' => 'Assault',
                'verdict' => 'Not Guilty',
                'sentence' => 'Acquitted',
                'verdict_date' => Carbon::now()->subDays(20),
                'judge' => 'Hon. Justice L. Banda',
                'prosecutor' => 'State Prosecutor C. Gondwe'
            ],
            [
                'id' => 4,
                'case_number' => 'CR/2023/061',
                'case_title' => 'State vs. Patricia Nyirenda',
                'defendant' => 'Patricia Nyirenda',
                'charges' => 'Drug Trafficking',
                'verdict' => 'Guilty',
                'sentence' => '15 years imprisonment',
                'verdict_date' => Carbon::now()->subDays(3),
                'judge' => 'Hon. Justice M. Phiri',
                'prosecutor' => 'State Prosecutor D. Msiska'
            ]
        ]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $verdicts = $verdicts->filter(function ($verdict) use ($search) {
                return stripos($verdict['case_number'], $search) !== false ||
                       stripos($verdict['case_title'], $search) !== false ||
                       stripos($verdict['defendant'], $search) !== false;
            });
        }

        // Apply verdict filter
        if ($request->filled('verdict_filter')) {
            $verdicts = $verdicts->where('verdict', $request->verdict_filter);
        }

        return view('court.verdicts', compact('verdicts'));
    }
}
