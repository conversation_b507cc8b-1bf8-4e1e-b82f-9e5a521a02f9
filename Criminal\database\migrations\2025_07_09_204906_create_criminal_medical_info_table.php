<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('criminal_medical_info', function (Blueprint $table) {
            $table->id();
            $table->foreignId('criminal_id')->constrained('criminals')->onDelete('cascade');

            // Medical Information
            $table->string('medical_condition', 200)->nullable();
            $table->text('allergies')->nullable();
            $table->text('medications')->nullable();
            $table->text('special_instructions')->nullable();

            // Blood type and other medical details
            $table->string('blood_type', 10)->nullable();
            $table->text('medical_history')->nullable();

            // Update tracking
            $table->date('last_updated')->nullable();
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['criminal_id']);
            $table->index(['last_updated']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('criminal_medical_info');
    }
};
