<?php

namespace App\Http\Controllers;

use App\Models\Criminal;
use App\Models\CriminalArrest;
use App\Models\CriminalBiometric;
use App\Models\CriminalRelationship;
use App\Models\CriminalMedicalInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CriminalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Criminal::with(['arrests', 'biometrics']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->searchByName($search)
                  ->orWhere('criminal_number', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by risk level
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->get('risk_level'));
        }

        // Filter by district
        if ($request->filled('district')) {
            $query->where('district', $request->get('district'));
        }

        // Filter wanted criminals
        if ($request->boolean('wanted_only')) {
            $query->wanted();
        }

        $criminals = $query->latest()->paginate(10);

        // Append query parameters to pagination links
        $criminals->appends($request->query());

        return view('criminals.index', compact('criminals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('criminals.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'alias' => 'nullable|string|max:100',
            'gender' => 'required|in:Male,Female,Other',
            'date_of_birth' => 'nullable|date|before:today',
            'place_of_birth' => 'nullable|string|max:200',
            'nationality' => 'nullable|string|max:100',
            'national_id' => 'nullable|string|max:20|unique:criminals,national_id',
            'height' => 'nullable|numeric|min:0|max:3',
            'weight' => 'nullable|numeric|min:0|max:500',
            'eye_color' => 'nullable|string|max:50',
            'hair_color' => 'nullable|string|max:50',
            'complexion' => 'nullable|string|max:50',
            'distinguishing_marks' => 'nullable|string',
            'scars_tattoos' => 'nullable|string',
            'phone_number' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'email' => 'nullable|email|max:100',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'district' => 'nullable|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:200',
            'emergency_contact_phone' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'emergency_contact_relationship' => 'nullable|string|max:100',
            'status' => 'required|in:Active,Inactive,Deceased,Deported',
            'risk_level' => 'required|in:Low,Medium,High,Critical',
            'is_wanted' => 'boolean',
            'is_repeat_offender' => 'boolean',
            'occupation' => 'nullable|string|max:200',
            'education_level' => 'nullable|string|max:100',
            'marital_status' => 'nullable|string|max:50',
            'known_associates' => 'nullable|string',
            'notes' => 'nullable|string',
            'mugshot' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'phone_number.regex' => 'Phone number must be in the format +265999123456 (Malawi format)',
            'emergency_contact_phone.regex' => 'Emergency contact phone must be in the format +265999123456 (Malawi format)',
            'mugshot.image' => 'The mugshot must be an image file.',
            'mugshot.mimes' => 'The mugshot must be a JPEG, PNG, or JPG file.',
            'mugshot.max' => 'The mugshot file size must not exceed 2MB.',
        ]);

        // Generate criminal number
        $validated['criminal_number'] = $this->generateCriminalNumber();
        $validated['created_by'] = Auth::id();
        $validated['updated_by'] = Auth::id();

        $criminal = Criminal::create($validated);

        // Handle photo upload
        if ($request->hasFile('mugshot')) {
            $criminal->addMediaFromRequest('mugshot')
                ->toMediaCollection('mugshots');
        }

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Criminal profile created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Criminal $criminal)
    {
        $criminal->load([
            'arrests.arrestingOfficer',
            'biometrics.collector',
            'relationships',
            'medicalInfo',
            'cases'
        ]);

        // Get users for modal dropdowns
        $users = \App\Models\User::orderBy('name')->get();

        return view('criminals.show', compact('criminal', 'users'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Criminal $criminal)
    {
        return view('criminals.edit', compact('criminal'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'alias' => 'nullable|string|max:100',
            'gender' => 'required|in:Male,Female,Other',
            'date_of_birth' => 'nullable|date|before:today',
            'place_of_birth' => 'nullable|string|max:200',
            'nationality' => 'nullable|string|max:100',
            'national_id' => 'nullable|string|max:20|unique:criminals,national_id,' . $criminal->id,
            'height' => 'nullable|numeric|min:0|max:3',
            'weight' => 'nullable|numeric|min:0|max:500',
            'eye_color' => 'nullable|string|max:50',
            'hair_color' => 'nullable|string|max:50',
            'complexion' => 'nullable|string|max:50',
            'distinguishing_marks' => 'nullable|string',
            'scars_tattoos' => 'nullable|string',
            'phone_number' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'email' => 'nullable|email|max:100',
            'current_address' => 'nullable|string',
            'permanent_address' => 'nullable|string',
            'district' => 'nullable|string|max:100',
            'traditional_authority' => 'nullable|string|max:100',
            'village' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:200',
            'emergency_contact_phone' => 'nullable|string|max:20|regex:/^\+265[0-9]{9}$/',
            'emergency_contact_relationship' => 'nullable|string|max:100',
            'status' => 'required|in:Active,Inactive,Deceased,Deported',
            'risk_level' => 'required|in:Low,Medium,High,Critical',
            'is_wanted' => 'boolean',
            'is_repeat_offender' => 'boolean',
            'occupation' => 'nullable|string|max:200',
            'education_level' => 'nullable|string|max:100',
            'marital_status' => 'nullable|string|max:50',
            'known_associates' => 'nullable|string',
            'notes' => 'nullable|string',
            'mugshot' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'remove_photo' => 'nullable|boolean',
        ], [
            'phone_number.regex' => 'Phone number must be in the format +265999123456 (Malawi format)',
            'emergency_contact_phone.regex' => 'Emergency contact phone must be in the format +265999123456 (Malawi format)',
            'mugshot.image' => 'The mugshot must be an image file.',
            'mugshot.mimes' => 'The mugshot must be a JPEG, PNG, or JPG file.',
            'mugshot.max' => 'The mugshot file size must not exceed 2MB.',
        ]);

        $validated['updated_by'] = Auth::id();

        $criminal->update($validated);

        // Handle photo removal
        if ($request->has('remove_photo') && $request->remove_photo) {
            $criminal->clearMediaCollection('mugshots');
        }

        // Handle new photo upload
        if ($request->hasFile('mugshot')) {
            // Clear existing photos first
            $criminal->clearMediaCollection('mugshots');
            // Add new photo
            $criminal->addMediaFromRequest('mugshot')
                ->toMediaCollection('mugshots');
        }

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Criminal profile updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Criminal $criminal)
    {
        $criminal->delete();

        return redirect()->route('criminals.index')
                        ->with('success', 'Criminal profile deleted successfully.');
    }

    /**
     * Store a new arrest record.
     */
    public function storeArrest(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'arrest_date' => 'required|date',
            'arrest_time' => 'nullable|date_format:H:i',
            'arrest_location' => 'required|string|max:300',
            'arrest_report_number' => 'nullable|string|max:100',
            'arresting_officer_id' => 'required|exists:users,id',
            'arrest_reason' => 'required|string',
            'charges' => 'required|string',
            'other_offenses' => 'nullable|string',
            'status' => 'required|in:Active,Closed,Pending,Dismissed',
            'notes' => 'nullable|string',
        ]);

        $criminal->arrests()->create($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Arrest record added successfully.');
    }

    /**
     * Update an arrest record.
     */
    public function updateArrest(Request $request, Criminal $criminal, $arrest)
    {
        $arrest = $criminal->arrests()->findOrFail($arrest);

        $validated = $request->validate([
            'arrest_date' => 'required|date',
            'arrest_time' => 'nullable|date_format:H:i',
            'arrest_location' => 'required|string|max:300',
            'arrest_report_number' => 'nullable|string|max:100',
            'arresting_officer_id' => 'required|exists:users,id',
            'arrest_reason' => 'required|string',
            'charges' => 'required|string',
            'other_offenses' => 'nullable|string',
            'status' => 'required|in:Active,Closed,Pending,Dismissed',
            'notes' => 'nullable|string',
        ]);

        $arrest->update($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Arrest record updated successfully.');
    }

    /**
     * Delete an arrest record.
     */
    public function destroyArrest(Criminal $criminal, $arrest)
    {
        $arrest = $criminal->arrests()->findOrFail($arrest);
        $arrest->delete();

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Arrest record deleted successfully.');
    }

    /**
     * Store a new biometric record.
     */
    public function storeBiometric(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'biometric_type' => 'required|in:Fingerprint,DNA,Photo,Iris Scan,Voice Print,Other',
            'description' => 'nullable|string',
            'collected_date' => 'required|date',
            'collected_by' => 'required|exists:users,id',
            'verified' => 'boolean',
            'notes' => 'nullable|string',
            'biometric_file' => 'nullable|file|mimes:jpeg,png,pdf|max:5120',
        ]);

        $biometric = $criminal->biometrics()->create($validated);

        // Handle file upload
        if ($request->hasFile('biometric_file')) {
            $biometric->addMediaFromRequest('biometric_file')
                ->toMediaCollection('biometric_files');
        }

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Biometric data added successfully.');
    }

    /**
     * Update a biometric record.
     */
    public function updateBiometric(Request $request, Criminal $criminal, $biometric)
    {
        $biometric = $criminal->biometrics()->findOrFail($biometric);

        $validated = $request->validate([
            'biometric_type' => 'required|in:Fingerprint,DNA,Photo,Iris Scan,Voice Print,Other',
            'description' => 'nullable|string',
            'collected_date' => 'required|date',
            'collected_by' => 'required|exists:users,id',
            'verified' => 'boolean',
            'notes' => 'nullable|string',
            'biometric_file' => 'nullable|file|mimes:jpeg,png,pdf|max:5120',
        ]);

        $biometric->update($validated);

        // Handle file upload
        if ($request->hasFile('biometric_file')) {
            $biometric->clearMediaCollection('biometric_files');
            $biometric->addMediaFromRequest('biometric_file')
                ->toMediaCollection('biometric_files');
        }

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Biometric data updated successfully.');
    }

    /**
     * Delete a biometric record.
     */
    public function destroyBiometric(Criminal $criminal, $biometric)
    {
        $biometric = $criminal->biometrics()->findOrFail($biometric);
        $biometric->delete();

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Biometric data deleted successfully.');
    }

    /**
     * Store a new relationship record.
     */
    public function storeRelationship(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'related_person_name' => 'required|string|max:200',
            'relationship_type' => 'required|in:Family,Associate,Gang,Witness,Victim',
            'relationship_detail' => 'nullable|string|max:100',
            'contact_info' => 'nullable|string|max:200',
            'notes' => 'nullable|string',
            'verified' => 'boolean',
            'last_contact' => 'nullable|date',
        ]);

        $criminal->relationships()->create($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Relationship record added successfully.');
    }

    /**
     * Update a relationship record.
     */
    public function updateRelationship(Request $request, Criminal $criminal, $relationship)
    {
        $relationship = $criminal->relationships()->findOrFail($relationship);

        $validated = $request->validate([
            'related_person_name' => 'required|string|max:200',
            'relationship_type' => 'required|in:Family,Associate,Gang,Witness,Victim',
            'relationship_detail' => 'nullable|string|max:100',
            'contact_info' => 'nullable|string|max:200',
            'notes' => 'nullable|string',
            'verified' => 'boolean',
            'last_contact' => 'nullable|date',
        ]);

        $relationship->update($validated);

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Relationship record updated successfully.');
    }

    /**
     * Delete a relationship record.
     */
    public function destroyRelationship(Criminal $criminal, $relationship)
    {
        $relationship = $criminal->relationships()->findOrFail($relationship);
        $relationship->delete();

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Relationship record deleted successfully.');
    }

    /**
     * Store or update medical information.
     */
    public function storeMedicalInfo(Request $request, Criminal $criminal)
    {
        $validated = $request->validate([
            'medical_condition' => 'nullable|string',
            'allergies' => 'nullable|string',
            'medications' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'blood_type' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'medical_history' => 'nullable|string',
        ]);

        $validated['last_updated'] = now();
        $validated['updated_by'] = auth()->id();

        $criminal->medicalInfo()->updateOrCreate(
            ['criminal_id' => $criminal->id],
            $validated
        );

        return redirect()->route('criminals.show', $criminal)
                        ->with('success', 'Medical information saved successfully.');
    }

    /**
     * Update medical information.
     */
    public function updateMedicalInfo(Request $request, Criminal $criminal)
    {
        return $this->storeMedicalInfo($request, $criminal);
    }

    /**
     * Generate a unique criminal number.
     */
    private function generateCriminalNumber(): string
    {
        do {
            $number = 'CRM' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (Criminal::where('criminal_number', $number)->exists());

        return $number;
    }

    /**
     * Display criminals list view.
     */
    public function list(Request $request)
    {
        return $this->index($request);
    }
}
