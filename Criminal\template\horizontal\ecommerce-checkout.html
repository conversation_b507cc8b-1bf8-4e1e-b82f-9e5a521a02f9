<!DOCTYPE html>
<html lang="en" data-layout="horizontal">


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/ecommerce-checkout.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:57 GMT -->
<head>
  <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

  <title>Order List | Dash UI - Bootstrap 5 Admin Dashboard Template</title>
</head>

<body>
  <main id="main-wrapper" class="main-wrapper">

    <div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>



 <!-- navbar horizontal -->
 <!-- navbar -->
<div class="navbar-horizontal nav-dashboard">
	<div class="container-fluid">
		<nav class="navbar navbar-expand-lg navbar-default navbar-dropdown p-0 py-lg-2">
			<div class="d-flex d-lg-block justify-content-between align-items-center w-100 w-lg-0 py-2 px-4 px-md-2 px-lg-0">
				<span class="d-lg-none">Menu</span>
				<!-- Button -->
				<button
					class="navbar-toggler collapsed ms-2"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbar-default"
					aria-controls="navbar-default"
					aria-expanded="false"
					aria-label="Toggle navigation"
				>
					<span class="icon-bar top-bar mt-0"></span>
					<span class="icon-bar middle-bar"></span>
					<span class="icon-bar bottom-bar"></span>
				</button>
			</div>
			<!-- Collapse -->
			<div class="collapse navbar-collapse px-6 px-lg-0" id="navbar-default">
				<ul class="navbar-nav">
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarDashboard" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-bs-display="static">Dashboard</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarDashboard">
							<li>
								<a class="dropdown-item" href="index.html">Project</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-analytics.html">Analytics</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-ecommerce.html">Ecommerce</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-crm.html">CRM</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-finance.html">Finance</a>
							</li>
							<li>
								<a class="dropdown-item" href="dashboard-blog.html">Blog</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarApps" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Apps</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarApps">
							<li>
								<a class="dropdown-item" href="calendar.html">Calendar</a>
							</li>
							<li>
								<a class="dropdown-item" href="apps-file-manager.html">File Manager</a>
							</li>

							<li>
								<a class="dropdown-item" href="chat-app.html">Chat</a>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Kanban</a>
								<ul class="dropdown-menu">
									<li>
										<a href="task-kanban-grid.html" class="dropdown-item">Board</a>
									</li>
									<li>
										<a href="task-kanban-list.html" class="dropdown-item">List</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Email</a>
								<ul class="dropdown-menu">
									<li>
										<a href="mail.html" class="dropdown-item">Inbox</a>
									</li>
									<li>
										<a href="mail-details.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="mail-draft.html" class="dropdown-item">Draft</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Ecommerce</a>
								<ul class="dropdown-menu">
									<li>
										<a href="ecommerce-products.html" class="dropdown-item">Products</a>
									</li>
									<li>
										<a href="ecommerce-products-details.html" class="dropdown-item">Prouduct Details</a>
									</li>
									<li>
										<a href="ecommerce-product-edit.html" class="dropdown-item">Add Product</a>
									</li>

									<li>
										<a href="ecommerce-order-list.html" class="dropdown-item">Orders</a>
									</li>
									<li>
										<a href="ecommerce-order-detail.html" class="dropdown-item">Order Details</a>
									</li>
									<li>
										<a href="ecommerce-cart.html" class="dropdown-item">Shopping Cart</a>
									</li>
									<li>
										<a href="ecommerce-checkout.html" class="dropdown-item">Checkout</a>
									</li>
									<li>
										<a href="ecommerce-customer.html" class="dropdown-item">Customers</a>
									</li>
									<li>
										<a href="ecommerce-seller.html" class="dropdown-item">Seller</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Project</a>
								<ul class="dropdown-menu">
									<li class="nav-item">
										<a class="dropdown-item" href="project-grid.html">Grid</a>
									</li>
									<li class="nav-item">
										<a class="dropdown-item" href="project-list.html">List</a>
									</li>

									<li class="dropdown-submenu dropend">
										<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Single</a>
										<ul class="dropdown-menu">
											<li class="nav-item">
												<a class="dropdown-item" href="project-overview.html">Overview</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-task.html">Task</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-budget.html">Budget</a>
											</li>

											<li class="nav-item">
												<a class="dropdown-item" href="project-files.html">File</a>
											</li>
											<li class="nav-item">
												<a class="dropdown-item" href="project-team.html">Team</a>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">CRM</a>
								<ul class="dropdown-menu">
									<li>
										<a href="crm-company.html" class="dropdown-item">Company</a>
									</li>
									<li>
										<a href="crm-contacts.html" class="dropdown-item">Contacts</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals.html">
											Deals
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
									<li>
										<a class="dropdown-item" href="deals-single.html">
											Deals Single
											<span class="badge text-bg-primary ms-2">New</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Invoice</a>
								<ul class="dropdown-menu">
									<li>
										<a href="invoice-list.html" class="dropdown-item">List</a>
									</li>
									<li>
										<a href="invoice-detail.html" class="dropdown-item">Details</a>
									</li>
									<li>
										<a href="invoice-generator.html" class="dropdown-item">Create Invoice</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Profile</a>
								<ul class="dropdown-menu">
									<li>
										<a href="profile-overview.html" class="dropdown-item">Overview</a>
									</li>
									<li>
										<a href="profile-project.html" class="dropdown-item">Project</a>
									</li>
									<li>
										<a href="profile-file.html" class="dropdown-item">Files</a>
									</li>
									<li>
										<a href="profile-team.html" class="dropdown-item">Team</a>
									</li>
									<li>
										<a href="profile-followers.html" class="dropdown-item">Followers</a>
									</li>
									<li>
										<a href="profile-activity.html" class="dropdown-item">Activity</a>
									</li>
									<li>
										<a class="dropdown-item" href="profile-settings.html">Settings</a>
									</li>
								</ul>
							</li>
							<li class="dropdown-submenu dropend">
								<a class="dropdown-item dropdown-list-group-item dropdown-toggle" href="#">Blog</a>
								<ul class="dropdown-menu">
									<li>
										<a class="dropdown-item" href="blog-author.html">Author</a>
									</li>
									<li>
										<a class="dropdown-item" href="blog-author-detail.html">Detail</a>
									</li>
									<li>
										<a class="dropdown-item" href="create-blog-post.html">Create Post</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarAuthentication" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Authentication</a>
						<ul class="dropdown-menu dropdown-menu-arrow" aria-labelledby="navbarAuthentication">
							<li>
								<a class="dropdown-item" href="sign-in.html">Sign In</a>
							</li>
							<li>
								<a class="dropdown-item" href="sign-up.html">Sign Up</a>
							</li>
							<li>
								<a class="dropdown-item" href="forget-password.html">Forgot Password</a>
							</li>
							<li>
								<a class="dropdown-item" href="maintenance.html">maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="layoutsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Layouts</a>
						<ul class="dropdown-menu dropdown-menu-start" aria-labelledby="layoutsDropdown">
							<li><span class="dropdown-header">Layouts</span></li>
							<li class="nav-item">
								<a class="dropdown-item" href="../index.html">Default</a>
							</li>

							<li class="nav-item">
								<a class="dropdown-item" href="index.html">Horizontal</a>
							</li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarPages" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Pages</a>
						<ul class="dropdown-menu" aria-labelledby="navbarPages">
							<li>
								<a class="dropdown-item" href="pricing.html">Pricing</a>
							</li>
							<li>
								<a class="dropdown-item" href="starter.html">Starter</a>
							</li>

							<li>
								<a class="dropdown-item" href="maintenance.html">Maintenance</a>
							</li>
							<li>
								<a class="dropdown-item" href="404-error.html">404 Error</a>
							</li>
						</ul>
					</li>
					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="navbarBaseUI" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Components</a>
						<div class="dropdown-menu dropdown-menu-xl" aria-labelledby="navbarBaseUI">
							<div class="row">
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/accordions.html" class="dropdown-item">Accordions</a>
										</li>
										<li class="nav-item">
											<a class="dropdown-item" href="components/alerts.html">Alert</a>
										</li>

										<li class="nav-item">
											<a href="components/badge.html" class="dropdown-item">Badge</a>
										</li>

										<li class="nav-item">
											<a href="components/breadcrumb.html" class="dropdown-item">Breadcrumb</a>
										</li>
										<li class="nav-item">
											<a href="components/buttons.html" class="dropdown-item">Buttons</a>
										</li>
										<li class="nav-item">
											<a href="components/button-group.html" class="dropdown-item">Button group</a>
										</li>
										<li class="nav-item">
											<a href="components/card.html" class="dropdown-item">Card</a>
										</li>
										<li class="nav-item">
											<a href="components/carousel.html" class="dropdown-item">Carousel</a>
										</li>
										<li class="nav-item">
											<a href="components/close-button.html" class="dropdown-item">Close Button</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/collapse.html" class="dropdown-item">Collapse</a>
										</li>
										<li class="nav-item">
											<a href="components/dropdowns.html" class="dropdown-item">Dropdowns</a>
										</li>
										<li class="nav-item">
											<a href="components/forms.html" class="dropdown-item">Forms</a>
										</li>

										<li class="nav-item">
											<a href="components/list-group.html" class="dropdown-item">List group</a>
										</li>
										<li class="nav-item">
											<a href="components/modal.html" class="dropdown-item">Modal</a>
										</li>
										<li class="nav-item">
											<a href="components/navs-tabs.html" class="dropdown-item">Navs and tabs</a>
										</li>
										<li class="nav-item">
											<a href="components/navbar.html" class="dropdown-item">Navbar</a>
										</li>
										<li class="nav-item">
											<a href="components/offcanvas.html" class="dropdown-item">Offcanvas</a>
										</li>
										<li class="nav-item">
											<a href="components/pagination.html" class="dropdown-item">Pagination</a>
										</li>
									</ul>
								</div>
								<div class="col-lg-4">
									<ul class="list-unstyled">
										<li class="nav-item">
											<a href="components/placeholders.html" class="dropdown-item">Placeholders</a>
										</li>
										<li class="nav-item">
											<a href="components/popovers.html" class="dropdown-item">Popovers</a>
										</li>
										<li class="nav-item">
											<a href="components/progress.html" class="dropdown-item">Progress</a>
										</li>
										<li class="nav-item">
											<a href="components/scrollspy.html" class="dropdown-item">Scrollspy</a>
										</li>
										<li class="nav-item">
											<a href="components/spinners.html" class="dropdown-item">Spinners</a>
										</li>
										<li class="nav-item">
											<a href="components/tables.html" class="dropdown-item">Tables</a>
										</li>
										<li class="nav-item">
											<a href="components/toasts.html" class="dropdown-item">Toasts</a>
										</li>
										<li class="nav-item">
											<a href="components/tooltips.html" class="dropdown-item">Tooltips</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i data-feather="more-horizontal" class="icon-xxs"></i>
						</a>
						<div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDropdown">
							<div class="list-group">
								<a class="list-group-item list-group-item-action border-0" href="../docs/index.html">
									<div class="d-flex align-items-center">
										<i data-feather="file-text" class="icon-sm text-primary"></i>

										<div class="ms-3">
											<h5 class="mb-0">Documentations</h5>
											<p class="mb-0 fs-6">Browse the all documentation</p>
										</div>
									</div>
								</a>
								<a class="list-group-item list-group-item-action border-0" href="../docs/changelog.html">
									<div class="d-flex align-items-center">
										<i data-feather="layers" class="icon-sm text-primary"></i>
										<div class="ms-3">
											<h5 class="mb-0">
												Changelog
												<span class="text-primary ms-1">v1.0.0</span>
											</h5>
											<p class="mb-0 fs-6">See what's new</p>
										</div>
									</div>
								</a>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</nav>
	</div>
</div>


    <!-- page content -->


      <div id="app-content">
      <!-- Container fluid -->
      <div class="app-content-area">
        <div class="container-fluid">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-12">
              <!-- Page header -->
              <div class="mb-5">
                <h3 class="mb-0 ">Checkout</h3>

              </div>
            </div>
          </div>
          <div>
            <!-- row -->

            <div class="row">
              <div class="col-xxl-8 col-12">
                <div>
                  <!-- stepper -->
                  <div id="stepperForm" class="bs-stepper">
                    <!-- Stepper Button -->
                    <div class="bs-stepper-header p-0 bg-transparent mb-4" role="tablist">
                      <div class="step" data-target="#test-l-1">
                        <button type="button" class="step-trigger" role="tab" id="stepperFormtrigger1"
                          aria-controls="test-l-1">
                          <span class="bs-stepper-circle me-2"><i data-feather="users" class="icon-xs"></i></span>
                          <span class="bs-stepper-label">Billing Info</span>
                        </button>
                      </div>
                      <div class="bs-stepper-line"></div>
                      <!-- Stepper Button -->
                      <div class="step" data-target="#test-l-2">
                        <button type="button" class="step-trigger" role="tab" id="stepperFormtrigger2"
                          aria-controls="test-l-2">
                          <span class="bs-stepper-circle me-2"><i data-feather="shopping-bag"
                              class="icon-xs"></i></span>
                          <span class="bs-stepper-label">Shipping Details</span>
                        </button>
                      </div>
                      <div class="bs-stepper-line"></div>
                      <!-- Stepper Button -->
                      <div class="step" data-target="#test-l-3">
                        <button type="button" class="step-trigger" role="tab" id="stepperFormtrigger3"
                          aria-controls="test-l-3">
                          <span class="bs-stepper-circle me-2"><i data-feather="credit-card" class="icon-xs"></i></span>
                          <span class="bs-stepper-label">Payment Info</span>
                        </button>
                      </div>

                    </div>
                    <!-- card -->
                    <div class="card">

                      <div class="card-body">
                        <!-- Stepper content -->
                        <div class="bs-stepper-content">
                          <form onSubmit="return false">
                            <!-- Content one -->
                            <div id="test-l-1" role="tabpanel" class="bs-stepper-pane fade"
                              aria-labelledby="stepperFormtrigger1">
                              <!-- heading -->
                              <div class="mb-5">
                                <h3 class="mb-1">Billing Information</h3>
                                <p class="mb-0">Please fill all information below
                                </p>
                              </div>
                              <!-- row -->
                              <div class="row">
                                <!-- input -->
                                <div class="mb-3 col-md-6">
                                  <label class="form-label" for="firstName">First Name</label>
                                  <input type="text" class="form-control" placeholder="Enter first name" id="firstName">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-md-6">
                                  <label class="form-label" for="lastName">Last Name</label>
                                  <input type="text" class="form-control" placeholder="Enter last name" id="lastName">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-md-6">
                                  <label class="form-label" for="email">Email (Optional)</label>
                                  <input type="email" class="form-control" placeholder="Enter email address" id="email">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-md-6">
                                  <label class="form-label" for="phone">Phone</label>
                                  <input type="text" class="form-control" placeholder="Enter phone number" id="phone">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-12">
                                  <label class="form-label" for="address">Address</label>
                                  <input type="text" class="form-control" placeholder="Enter address" id="address">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-12">
                                  <label class="form-label" for="town">Town / City</label>
                                  <input type="text" class="form-control" placeholder="Enter City" id="town">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-12">
                                  <label class="form-label" for="state">State</label>
                                  <input type="text" class="form-control" placeholder="Enter State" id="state">
                                </div>
                                <!-- input -->
                                <div class="mb-3 col-12">
                                  <label class="form-label" for="zip">Zip / Postal Code</label>
                                  <input type="text" class="form-control" placeholder="Zip / Postal Code" id="zip">
                                </div>
                                <!-- select -->
                                <div class="mb-3 col-12">
                                  <label class="form-label">Country</label>
                                  <select class="form-select">
                                    <option selected>Select Country</option>
                                    <option value="1">India</option>
                                    <option value="2">UK</option>
                                    <option value="3">US</option>
                                  </select>
                                </div>
                                <!-- checkbox -->
                                <div class="mb-3 col-12">
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="" id="shipAddress">
                                    <label class="form-check-label" for="shipAddress">
                                      Ship to different address ?
                                    </label>
                                  </div>
                                </div>
                              </div>

                              <!-- Button -->
                              <div class="d-flex justify-content-end">
                                <button class="btn btn-primary" onclick="stepperForm.next()">
                                  Proceed to Shipping <i class="fe fe-shopping-bag ms-1"></i>
                                </button>
                              </div>
                            </div>
                            <!-- Content two -->
                            <div id="test-l-2" role="tabpanel" class="bs-stepper-pane fade"
                              aria-labelledby="stepperFormtrigger2">
                              <!-- text -->
                              <div class="mb-5">
                                <h3 class="mb-1">Shipping Information</h3>
                                <p class="mb-0">Fill the form below in order to send you the orders invoice.
                                </p>
                              </div>
                              <!-- text -->
                              <div class="d-flex justify-content-between align-items-center mb-2">
                                <h4 class="mb-0">Saved Address</h4>
                                <a href="#!" class="btn btn-outline-light text-dark" data-bs-toggle="modal"
                                  data-bs-target="#addNewAddress">Add new address</a>
                              </div>
                              <!-- row -->
                              <div class="row">
                                <div class="col-lg-6 col-12 mb-4">
                                  <!-- form -->
                                  <div class="border p-4 rounded-3">
                                    <div class="form-check mb-2">
                                      <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="homeRadio">
                                      <label class="form-check-label text-dark " for="homeRadio">
                                        Home
                                      </label>
                                    </div>
                                    <!-- address -->
                                    <p class="mb-0">3812 Orchard Street<br>

                                      Bloomington,<br>

                                      Minnesota 55431,<br>

                                      United States</p>
                                  </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-4">
                                  <!-- input -->
                                  <div class="border p-4 rounded-3">
                                    <div class="form-check mb-2">
                                      <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="officeRadio">
                                      <label class="form-check-label text-dark " for="officeRadio">
                                        Office
                                      </label>
                                    </div>
                                    <!-- address -->
                                    <p class="mb-0">3853 Coal Road<br>

                                      Tannersville, <br>

                                      Pennsylvania, 18372 <br>

                                      United States</p>
                                  </div>
                                </div>
                              </div>
                              <div>
                                <!-- heading -->
                                <h4 class="mb-4">Shipping Method</h4>
                                <!-- card -->
                                <div class="card card-bordered shadow-none mb-2">
                                  <!-- card body -->
                                  <div class="card-body">
                                    <!-- form check -->
                                    <div class="d-flex">
                                      <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                          id="freeDelivery">
                                        <label class="form-check-label ms-2" for="freeDelivery">

                                        </label>
                                      </div>
                                      <div>
                                        <h5 class="mb-1"> Free Delivery</h5>
                                        <span class="fs-6">Expected Delivery 3 to 5 Days</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <!-- card -->
                                <div class="card card-bordered shadow-none mb-2">
                                  <!-- card body -->
                                  <div class="card-body">
                                    <!-- form check -->
                                    <div class="d-md-flex">
                                      <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                          id="nextDelivery">
                                        <label class="form-check-label ms-2 w-100" for="nextDelivery">

                                        </label>

                                      </div>
                                      <div class="d-flex justify-content-between align-items-center w-100">
                                        <div class="d-flex align-items-start">
                                          <!-- img -->
                                          <img src="../assets/images/svg/payment-logo-fedex.svg" alt="Image">
                                          <!-- text -->
                                          <div class="ms-2">
                                            <h5 class="mb-1"> FedEx Next Day Delivery</h5>
                                            <p class="mb-0 fs-6">No Delivery on Public Holidays</p>
                                          </div>
                                        </div>
                                        <div>
                                          <!-- heading -->
                                          <h3 class="mb-0">$19.99
                                          </h3>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <!-- card -->
                                <div class="card card-bordered shadow-none">
                                  <!-- card body -->
                                  <div class="card-body">
                                    <div class="d-md-flex">
                                      <div class="form-check">
                                        <!-- input -->
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                          id="DHLExpress">
                                        <label class="form-check-label ms-2 w-100" for="DHLExpress">

                                        </label>
                                      </div>
                                      <div class="d-flex justify-content-between align-items-center w-100">
                                        <!-- img -->
                                        <div class="d-flex align-items-start">
                                          <img src="../assets/images/svg/payment-logo-dhl.svg" alt="Image">
                                          <!-- text -->
                                          <div class="ms-2">
                                            <h5 class="mb-1">DHL Express</h5>
                                            <p class="mb-0 fs-6">1 Day Delivery</p>
                                          </div>
                                        </div>
                                        <!-- text -->
                                        <div>
                                          <h3 class="mb-0">$8.99
                                          </h3>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <!-- Button -->
                              <div class="d-md-flex justify-content-between  mt-4">
                                <button class="btn btn-outline-primary mb-2 mb-md-0" onclick="stepperForm.previous()">
                                  Back to Info
                                </button>
                                <!-- Button -->
                                <button class="btn btn-primary" onclick="stepperForm.next()">
                                  Continue to Payment <i class="fe fe-credit-card ms-2"></i>
                                </button>
                              </div>
                            </div>
                            <!-- Content three -->
                            <div id="test-l-3" role="tabpanel" class="bs-stepper-pane fade"
                              aria-labelledby="stepperFormtrigger3">
                              <!-- Card -->
                              <div class="mb-5">
                                <h3 class="mb-1">Payment selection</h3>
                                <p class="mb-0">Please select and enter your billing information.
                                </p>
                              </div>
                              <!-- Card -->
                              <div class="card card-bordered shadow-none mb-2">
                                <!-- card body -->
                                <div class="card-body">
                                  <div class="d-flex">
                                    <div class="form-check">
                                      <!-- checkbox -->
                                      <input class="form-check-input" type="radio" name="flexRadioDefault" id="paypal">
                                      <label class="form-check-label ms-2" for="paypal">

                                      </label>
                                    </div>
                                    <div>
                                      <h5 class="mb-1"> Payment with Paypal</h5>
                                      <p class="mb-0 fs-6">You will be redirected to PayPal website to complete your
                                        purchase
                                        securely.</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <!-- card -->
                              <div class="card card-bordered shadow-none mb-2">
                                <!-- card body -->
                                <div class="card-body">
                                  <div class="d-flex mb-4">
                                    <div class="form-check ">
                                      <!-- input -->
                                      <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="creditdebitcard">
                                      <label class="form-check-label ms-2" for="creditdebitcard">

                                      </label>
                                    </div>
                                    <div>
                                      <h5 class="mb-1"> Credit / Debit Card</h5>
                                      <p class="mb-0 fs-6">Safe money transfer using your bank accou k account. We
                                        support
                                        Mastercard tercard, Visa, Discover and Stripe.</p>
                                    </div>
                                  </div>
                                  <div class="row">
                                    <div class="col-12">
                                      <!-- input -->
                                      <div class="mb-3">
                                        <label class="form-label" for="cc-mask">Card
                                          Number

                                        </label>
                                        <div class="input-group">
                                          <input type="text" class="form-control"  name="cc-mask" id="cc-mask"
                                            data-inputmask="'mask': '9999 9999 9999 9999'" inputmode="numeric"
                                            placeholder="xxxx-xxxx-xxxx-xxxx" required />


                                        </div>
                                        <small class="text-muted">Full name as displayed on card.</small>
                                      </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                      <!-- input -->
                                      <div class="mb-3 mb-lg-0">
                                        <label class="form-label">Name on card </label>
                                        <input type="text" class="form-control" placeholder="Enter your first name">
                                      </div>
                                    </div>
                                    <div class="col-md-3 col-12">
                                      <!-- input -->
                                      <div class="mb-3  mb-lg-0">
                                        <label class="form-label">Expiry date </label>
                                        <input type="text" class="form-control flatpickr">
                                      </div>
                                    </div>
                                    <div class="col-md-3 col-12">
                                      <!-- input -->
                                      <div class="mb-3  mb-lg-0">
                                        <label for="cvv" class="form-label ">CVV Code
                                          <a href="#!" class="texttooltip" data-template="trashOne">
                                            <i data-feather="help-circle" class="icon-xs"></i>
                                          </a>
                                          </label>

                                        <div id="trashOne" class="d-none">
                                          <span>A 3 - digit number, typically printed on the back
                                            of a card.</span>
                                        </div>

                                        <input type="password" class="cc-inputmask form-control" name="cvv" id="cvv"
                                          placeholder="xxx" maxlength="3">

                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <!-- card -->
                              <div class="card card-bordered shadow-none mb-2">
                                <!-- card body -->
                                <div class="card-body">
                                  <!-- check input -->
                                  <div class="d-flex">
                                    <div class="form-check">
                                      <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="payoneer">
                                      <label class="form-check-label ms-2" for="payoneer">

                                      </label>
                                    </div>
                                    <div>
                                      <h5 class="mb-1"> Pay with Payoneer</h5>
                                      <p class="mb-0 fs-6">You will be redirected to Payoneer website to complete your
                                        purchase securely.</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <!-- card -->
                              <div class="card card-bordered shadow-none">
                                <div class="card-body">
                                  <!-- check input -->
                                  <div class="d-flex">
                                    <div class="form-check">
                                      <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="cashonDelivery">
                                      <label class="form-check-label ms-2" for="cashonDelivery">

                                      </label>
                                    </div>
                                    <div>
                                      <h5 class="mb-1"> Cash on Delivery</h5>
                                      <p class="mb-0 fs-6">Pay with cash when your order is delivered.</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <!-- Button -->
                              <div class="d-flex justify-content-between">
                                <!-- Button -->
                                <button class="btn btn-outline-primary mt-3" onclick="stepperForm.previous()">
                                  Back to shipping
                                </button>
                                <!-- Button -->
                                <button type="submit" class="btn btn-primary mt-3"
                                  onclick=" location.href='order-summary.html' ">
                                  Complete Order
                                </button>
                              </div>
                            </div>

                          </form>
                        </div>
                      </div>
                    </div>





                  </div>

                </div>
              </div>
              <div class="col-xxl-4 col-12">
                <div class="card mt-5 mt-xxl-0">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Order Summary</h4>
                    <a href="#!" class="btn btn-primary btn-sm">Edit Cart</a>
                  </div>
                  <div class="card-body">


                    <div class="d-md-flex">
                      <div>
                        <img src="../assets/images/ecommerce/product-1.jpg" alt="Image" class="img-4by3-md rounded">
                      </div>
                      <div class="ms-md-4 mt-2">
                        <h4 class="mb-1 ">
                          <a href="#!" class="text-inherit">
                            Women Shoes
                          </a>
                        </h4>
                        <h5>$49.00</h5>



                      </div>

                    </div>
                    <hr class="my-3">

                    <div class="d-md-flex">
                      <div>
                        <img src="../assets/images/ecommerce/product-2.jpg" alt="Image" class="img-4by3-md rounded">
                      </div>
                      <div class="ms-md-4 mt-2">
                        <h4 class="mb-1 ">
                          <a href="#!" class="text-inherit">
                            Black Round Sunglasses
                          </a>
                        </h4>
                        <h5>$79.00</h5>

                      </div>
                    </div>
                  </div>
                  <div class="card-body border-top pt-2">
                    <ul class="list-group list-group-flush mb-0 ">
                      <li class="d-flex justify-content-between list-group-item px-0">
                        <span>Subtotal</span>
                        <span class="text-dark ">$128.00</span>
                      </li>
                      <li class="d-flex justify-content-between list-group-item px-0">
                        <span>Shipping</span>
                        <span class="text-dark ">$0.00</span>
                      </li>
                      <li class="d-flex justify-content-between list-group-item px-0">
                        <span>Discount</span>
                        <span class="text-dark ">$0.00</span>
                      </li>
                      <li class="d-flex justify-content-between list-group-item px-0 pb-0">
                        <span>Tax</span>
                        <span class="text-dark ">$0.00</span>
                      </li>

                    </ul>
                  </div>
                  <div class="card-footer">
                    <div class="d-flex justify-content-between list-group-item px-0 pb-0">
                      <span class="fs-4  text-dark">Grand Total</span>
                      <span class=" text-dark">$128.00</span>
                    </div>
                  </div>

                </div>

              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- flatpickr -->
  <script src="../assets/libs/flatpickr/dist/flatpickr.min.js"></script>


  <!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

  <script src="../assets/libs/inputmask/dist/jquery.inputmask.min.js"></script>
  <script src="../assets/js/vendors/inputmask.js"></script>
   <!-- popper js -->
   <script src="../assets/libs/%40popperjs/core/dist/umd/popper.min.js"></script>
   <!-- tippy js -->
   <script src="../assets/libs/tippy.js/dist/tippy-bundle.umd.min.js"></script>
 <script src="../assets/js/vendors/tooltip.js"></script>
   <!-- Scripts -->
   <script src="../assets/libs/bs-stepper/dist/js/bs-stepper.min.js"></script>
   <script src="../assets/js/vendors/beStepper.js"></script>



</body>


<!-- Mirrored from dashui.codescandy.com/dashuipro/horizontal/ecommerce-checkout.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:30:57 GMT -->
</html>