<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="author" content="{{ config('app.name') }}" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/images/favicon/favicon.ico') }}" />

    <!-- Color modes -->
    <script src="{{ asset('assets/js/vendors/color-modes.js') }}"></script>

    <!-- Libs CSS -->
    <link href="{{ asset('assets/libs/bootstrap-icons/font/bootstrap-icons.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/libs/@mdi/font/css/materialdesignicons.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/libs/simplebar/dist/simplebar.min.css') }}" rel="stylesheet" />

    <!-- Theme CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">

    <!-- Layout Fix CSS -->
    <style>
        /* Ensure app-content has proper top margin to account for fixed header */
        #app-content {
            margin-top: 70px; /* Height of the fixed header */
            margin-bottom: 60px; /* Height for the footer */
        }

        /* Ensure proper padding for content area */
        .app-content-area {
            padding: 1.5rem;
        }

        /* Footer positioning */
        #main-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        #main-wrapper footer {
            margin-top: auto;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 991.98px) {
            #app-content {
                margin-top: 60px; /* Smaller header on mobile */
                margin-bottom: 60px; /* Footer space on mobile */
            }

            .app-content-area {
                padding: 1rem;
            }
        }
    </style>

    <!-- Page specific CSS -->
    @stack('styles')

    <title>@yield('title', config('app.name'))</title>

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Custom Branding Styles -->
    <style>
        .malawi-flag {
            width: 24px;
            height: 16px;
            background: linear-gradient(to bottom, #000 33%, #dc2626 33% 66%, #16a34a 66%);
            border-radius: 3px;
            display: inline-block;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .auth-logo-container {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .navbar-brand {
            font-weight: 700;
            text-decoration: none !important;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            text-decoration: none !important;
            transform: scale(1.05);
        }

        .app-initials {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 800;
            letter-spacing: 1.5px;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>

<body>
    <main id="main-wrapper" class="main-wrapper">
        <!-- Header -->
        <div class="header">
            @include('components.navbar')
        </div>

        <!-- Sidebar -->
        @include('components.sidebar')

        <!-- Page content -->
        <div id="app-content">
            <div class="app-content-area">
                <div class="container-fluid">
                    <!-- Flash Messages -->
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <!-- Main Content Area -->
                    @yield('content')
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-light border-top mt-auto py-3">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <img src="{{ asset('images/company-logos/logo-no-bg.png') }}" alt="Gluhen Investment" class="me-2" style="width: 24px; height: 24px; object-fit: contain;">
                            <span class="text-muted small">
                                &copy; {{ date('Y') }}
                                <a href="https://gluheninvestment.com" target="_blank" class="text-decoration-none text-primary">
                                    Gluhen Investment
                                </a>
                                . All rights reserved.
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <span class="text-muted small">
                            Powered by
                            <a href="https://gluheninvestment.com" target="_blank" class="text-decoration-none text-primary fw-medium">
                                Gluhen Investment
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </footer>
    </main>

    <!-- Modals -->
    @stack('modals')

    <!-- Scripts -->
    <script src="{{ asset('assets/libs/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/dist/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- Page specific scripts -->
    @stack('scripts')

    <!-- Initialize Feather Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather Icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Debug: Check if elements exist
            console.log('Main wrapper:', document.getElementById('main-wrapper'));
            console.log('Nav toggle:', document.getElementById('nav-toggle'));
            console.log('App content:', document.getElementById('app-content'));
        });
    </script>

    <!-- Livewire Scripts -->
    @livewireScripts
</body>
</html>
