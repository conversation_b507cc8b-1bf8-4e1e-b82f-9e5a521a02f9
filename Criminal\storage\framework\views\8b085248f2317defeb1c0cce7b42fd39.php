<?php $__env->startSection('title', 'Criminal Profiles  - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Criminal Profiles</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'Criminal Profiles', 'url' => route('criminals.index')]
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'Criminal Profiles', 'url' => route('criminals.index')]
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div>
            <a href="<?php echo e(route('criminals.create')); ?>" class="btn btn-primary">
                <i data-feather="user-plus" class="icon-xs me-2"></i>
                Add New Criminal
            </a>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Advanced Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true">
                    <i data-feather="chevron-up" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form class="row g-3" method="GET" action="<?php echo e(route('criminals.index')); ?>" id="criminalFilterForm">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Name, Criminal Number, or National ID">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="Active" <?php echo e(request('status') == 'Active' ? 'selected' : ''); ?>>Active</option>
                            <option value="Inactive" <?php echo e(request('status') == 'Inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="Deceased" <?php echo e(request('status') == 'Deceased' ? 'selected' : ''); ?>>Deceased</option>
                            <option value="Deported" <?php echo e(request('status') == 'Deported' ? 'selected' : ''); ?>>Deported</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Risk Level</label>
                        <select class="form-select" name="risk_level">
                            <option value="">All Levels</option>
                            <option value="Low" <?php echo e(request('risk_level') == 'Low' ? 'selected' : ''); ?>>Low</option>
                            <option value="Medium" <?php echo e(request('risk_level') == 'Medium' ? 'selected' : ''); ?>>Medium</option>
                            <option value="High" <?php echo e(request('risk_level') == 'High' ? 'selected' : ''); ?>>High</option>
                            <option value="Critical" <?php echo e(request('risk_level') == 'Critical' ? 'selected' : ''); ?>>Critical</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">District</label>
                        <select class="form-select" name="district">
                            <option value="">All Districts</option>
                            <option value="Lilongwe" <?php echo e(request('district') == 'Lilongwe' ? 'selected' : ''); ?>>Lilongwe</option>
                            <option value="Blantyre" <?php echo e(request('district') == 'Blantyre' ? 'selected' : ''); ?>>Blantyre</option>
                            <option value="Mzuzu" <?php echo e(request('district') == 'Mzuzu' ? 'selected' : ''); ?>>Mzuzu</option>
                            <option value="Zomba" <?php echo e(request('district') == 'Zomba' ? 'selected' : ''); ?>>Zomba</option>
                            <option value="Kasungu" <?php echo e(request('district') == 'Kasungu' ? 'selected' : ''); ?>>Kasungu</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs me-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="wanted_only" value="1"
                                   <?php echo e(request('wanted_only') ? 'checked' : ''); ?>>
                            <label class="form-check-label">
                                Show wanted criminals only
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary"><?php echo e($criminals->total()); ?></h4>
                    <p class="mb-0">Total Results</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-warning"><?php echo e($criminals->where('is_wanted', true)->count()); ?></h4>
                    <p class="mb-0">Wanted</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-danger"><?php echo e($criminals->whereIn('risk_level', ['High', 'Critical'])->count()); ?></h4>
                    <p class="mb-0">High Risk</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-success"><?php echo e($criminals->where('status', 'Active')->count()); ?></h4>
                    <p class="mb-0">Active</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Criminals List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Criminal Records</h4>
                </div>
                <div class="card-body">
                    <?php if($criminals->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Criminal Number</th>
                                        <th>Name</th>
                                        <th>Age</th>
                                        <th>District</th>
                                        <th>Status</th>
                                        <th>Risk Level</th>
                                        <th>Wanted</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $criminals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $criminal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($criminal->getFirstMediaUrl('mugshots')): ?>
                                                <img src="<?php echo e($criminal->getFirstMediaUrl('mugshots', 'thumb')); ?>" 
                                                     alt="Mugshot" class="rounded-circle" width="40" height="40">
                                            <?php else: ?>
                                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i data-feather="user" class="icon-sm text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo e($criminal->criminal_number); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e($criminal->full_name); ?></strong>
                                                <?php if($criminal->alias): ?>
                                                    <br><small class="text-muted">Alias: <?php echo e($criminal->alias); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?php echo e($criminal->age ?? 'N/A'); ?></td>
                                        <td><?php echo e($criminal->district ?? 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo e($criminal->status == 'Active' ? 'success' : 'secondary'); ?>">
                                                <?php echo e($criminal->status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo e($criminal->risk_level == 'Critical' ? 'danger' : 
                                                ($criminal->risk_level == 'High' ? 'warning' : 
                                                ($criminal->risk_level == 'Medium' ? 'info' : 'success'))); ?>">
                                                <?php echo e($criminal->risk_level); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($criminal->is_wanted): ?>
                                                <span class="badge bg-danger">
                                                    <i data-feather="alert-triangle" class="icon-xs me-1"></i>
                                                    Wanted
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">No</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo e(route('criminals.show', $criminal)); ?>">
                                                            <i data-feather="eye" class="icon-xs me-2"></i>
                                                            View Profile
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo e(route('criminals.edit', $criminal)); ?>">
                                                            <i data-feather="edit" class="icon-xs me-2"></i>
                                                            Edit
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="POST" action="<?php echo e(route('criminals.destroy', $criminal)); ?>" 
                                                              onsubmit="return confirm('Are you sure you want to delete this criminal record?')">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i data-feather="trash-2" class="icon-xs me-2"></i>
                                                                Delete
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                    </div>

                    <!-- Pagination -->
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                Showing <strong><?php echo e($criminals->firstItem()); ?></strong> to <strong><?php echo e($criminals->lastItem()); ?></strong> of <strong><?php echo e($criminals->total()); ?></strong> criminals
                            </div>
                            <nav aria-label="Criminal pagination">
                                <?php echo e($criminals->links('pagination::bootstrap-4')); ?>

                            </nav>
                        </div>
                    </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i data-feather="users" class="icon-xl text-muted mb-3"></i>
                            <h5 class="text-muted">No criminal records found</h5>
                            <p class="text-muted">Try adjusting your search criteria or add a new criminal profile.</p>
                            <a href="<?php echo e(route('criminals.create')); ?>" class="btn btn-primary">
                                <i data-feather="user-plus" class="icon-xs me-2"></i>
                                Add New Criminal
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/criminals/index.blade.php ENDPATH**/ ?>