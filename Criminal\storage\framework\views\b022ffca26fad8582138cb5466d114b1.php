<?php if (isset($component)) { $__componentOriginal2794fdd104a4eebb18042e6478dc49e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2794fdd104a4eebb18042e6478dc49e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.criminal-form','data' => ['criminal' => $criminal,'action' => route('criminals.update', $criminal),'method' => 'PUT','title' => 'Edit Criminal Profile: '.e($criminal->full_name).'','breadcrumbs' => [
        ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
        ['title' => $criminal->full_name, 'url' => route('criminals.show', $criminal)],
        ['title' => 'Edit']
    ],'backUrl' => route('criminals.show', $criminal),'submitText' => 'Update Criminal Profile']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('criminal-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['criminal' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($criminal),'action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('criminals.update', $criminal)),'method' => 'PUT','title' => 'Edit Criminal Profile: '.e($criminal->full_name).'','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
        ['title' => $criminal->full_name, 'url' => route('criminals.show', $criminal)],
        ['title' => 'Edit']
    ]),'back-url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('criminals.show', $criminal)),'submit-text' => 'Update Criminal Profile']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2794fdd104a4eebb18042e6478dc49e6)): ?>
<?php $attributes = $__attributesOriginal2794fdd104a4eebb18042e6478dc49e6; ?>
<?php unset($__attributesOriginal2794fdd104a4eebb18042e6478dc49e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2794fdd104a4eebb18042e6478dc49e6)): ?>
<?php $component = $__componentOriginal2794fdd104a4eebb18042e6478dc49e6; ?>
<?php unset($__componentOriginal2794fdd104a4eebb18042e6478dc49e6); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/criminals/edit.blade.php ENDPATH**/ ?>