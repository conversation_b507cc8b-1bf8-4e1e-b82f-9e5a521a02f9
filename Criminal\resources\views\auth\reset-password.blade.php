<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - {{ config('app.name') }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #3b82f6;
            --accent-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            position: relative;
            padding: 2rem 0;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .reset-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            min-height: calc(100vh - 4rem);
        }

        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            width: 100%;
            overflow-y: auto;
        }

        .malawi-flag {
            width: 40px;
            height: 30px;
            background: linear-gradient(to bottom, #000 33.33%, #e31e24 33.33%, #e31e24 66.66%, #00a651 66.66%);
            border-radius: 4px;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .malawi-flag::after {
            content: '☀';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #e31e24;
            font-size: 12px;
            text-shadow: 0 0 2px rgba(0,0,0,0.3);
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }

        .form-control.is-valid {
            border-color: #16a34a;
            box-shadow: 0 0 0 0.2rem rgba(22, 163, 74, 0.25);
        }

        .form-control.is-invalid {
            border-color: #dc2626;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0;
            }

            .reset-container {
                padding: 1rem;
                min-height: calc(100vh - 2rem);
            }

            .reset-card {
                padding: 1.5rem;
                max-height: 95vh;
            }
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
        }

        .btn-outline-secondary {
            border: 2px solid #6b7280;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(107, 114, 128, 0.3);
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .alert {
            border-radius: 12px;
            border: none;
        }

        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: #dc2626;
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: #16a34a;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shapes::before,
        .floating-shapes::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-shapes::before {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-shapes::after {
            width: 150px;
            height: 150px;
            bottom: 10%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .app-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-top: 1rem;
            letter-spacing: 1px;
        }

        .app-subtitle {
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .icon-circle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }

        .password-requirements {
            background: rgba(30, 64, 175, 0.05);
            border: 1px solid rgba(30, 64, 175, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .password-requirements ul {
            margin: 0;
            padding-left: 1.2rem;
        }

        .password-requirements li {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
        }

        .password-requirements li.valid {
            color: #16a34a;
        }

        .password-requirements li.valid::before {
            content: '✓ ';
            color: #16a34a;
            font-weight: bold;
        }

        .password-requirements li.invalid {
            color: #dc2626;
        }

        .password-requirements li.invalid::before {
            content: '✗ ';
            color: #dc2626;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="floating-shapes"></div>

    <div class="container">
        <div class="reset-container">
            <div class="reset-card">
                <!-- Logo Section -->
                <div class="logo-section">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <img src="{{ asset('images/logo.png') }}" alt="Logo" class="me-3" style="width: 50px; height: 50px; object-fit: contain;">
                        <div class="app-name">{{ app_initials() }}</div>
                    </div>
                    <div class="app-subtitle">Malawi Police Service</div>

                    <h4 class="mt-3 mb-0 text-dark">Reset Your Password</h4>
                    <p class="text-muted">Enter your new password below to complete the reset process.</p>
                </div>

                <!-- Validation Errors -->
                @if ($errors->any())
                    <div class="alert alert-danger mb-3">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- Status Message -->
                @session('status')
                    <div class="alert alert-success mb-3">
                        <i data-feather="check-circle" class="me-2" style="width: 16px; height: 16px;"></i>
                        {{ $value }}
                    </div>
                @endsession

                <!-- Reset Password Form -->
                <form method="POST" action="{{ route('password.update') }}">
                    @csrf

                    <input type="hidden" name="token" value="{{ $request->route('token') }}">

                    <div class="mb-3">
                        <label for="email" class="form-label fw-semibold">Email Address</label>
                        <input id="email" type="email" class="form-control" name="email"
                               value="{{ old('email', $request->email) }}" required autofocus autocomplete="username"
                               placeholder="Enter your email address" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label fw-semibold">New Password</label>
                        <input id="password" type="password" class="form-control" name="password"
                               required autocomplete="new-password"
                               placeholder="Enter your new password">
                    </div>

                    <div class="mb-4">
                        <label for="password_confirmation" class="form-label fw-semibold">Confirm New Password</label>
                        <input id="password_confirmation" type="password" class="form-control" name="password_confirmation"
                               required autocomplete="new-password"
                               placeholder="Confirm your new password">
                    </div>

                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i data-feather="check" class="me-2" style="width: 16px; height: 16px;"></i>
                            Reset Password
                        </button>
                    </div>

                    <div class="text-center">
                        <a href="{{ route('login') }}" class="btn btn-outline-secondary">
                            <i data-feather="arrow-left" class="me-2" style="width: 16px; height: 16px;"></i>
                            Back to Login
                        </a>
                    </div>
                </form>

                <!-- Footer -->
                <div class="text-center mt-4 pt-3 border-top">
                    <small class="text-muted">
                        &copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Initialize Feather Icons -->
    <script>
        feather.replace();

        // Password validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('password_confirmation');

            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    validatePassword(this.value);
                });
            }

            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    validatePasswordMatch();
                });
            }

            function validatePassword(password) {
                // Length requirement (minimum 8 characters)
                const lengthReq = document.getElementById('length-req');
                if (password.length >= 8) {
                    lengthReq.className = 'valid';
                } else {
                    lengthReq.className = 'invalid';
                }

                // Uppercase letter (recommended)
                const uppercaseReq = document.getElementById('uppercase-req');
                if (/[A-Z]/.test(password)) {
                    uppercaseReq.className = 'valid';
                } else {
                    uppercaseReq.className = password.length > 0 ? 'invalid' : '';
                }

                // Lowercase letter (recommended)
                const lowercaseReq = document.getElementById('lowercase-req');
                if (/[a-z]/.test(password)) {
                    lowercaseReq.className = 'valid';
                } else {
                    lowercaseReq.className = password.length > 0 ? 'invalid' : '';
                }

                // Number (recommended)
                const numberReq = document.getElementById('number-req');
                if (/[0-9]/.test(password)) {
                    numberReq.className = 'valid';
                } else {
                    numberReq.className = password.length > 0 ? 'invalid' : '';
                }

                // Special character (recommended)
                const specialReq = document.getElementById('special-req');
                if (/[^A-Za-z0-9]/.test(password)) {
                    specialReq.className = 'valid';
                } else {
                    specialReq.className = password.length > 0 ? 'invalid' : '';
                }

                validatePasswordMatch();
            }

            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword.length > 0) {
                    if (password === confirmPassword) {
                        confirmPasswordInput.classList.remove('is-invalid');
                        confirmPasswordInput.classList.add('is-valid');
                    } else {
                        confirmPasswordInput.classList.remove('is-valid');
                        confirmPasswordInput.classList.add('is-invalid');
                    }
                } else {
                    confirmPasswordInput.classList.remove('is-valid', 'is-invalid');
                }
            }
        });
    </script>
</body>
</html>
