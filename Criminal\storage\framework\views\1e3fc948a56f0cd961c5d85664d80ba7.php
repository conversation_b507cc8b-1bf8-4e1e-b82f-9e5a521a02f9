<?php $__env->startSection('title', 'Create New User  - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New User</h1>
            <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'Create New User']
            ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                ['title' => 'System'],
                ['title' => 'User Management', 'url' => route('users.index')],
                ['title' => 'Create New User']
            ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
        </div>
        <div>
            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-outline-secondary">
                <i data-feather="arrow-left" class="icon-xs me-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <form method="POST" action="<?php echo e(route('users.store')); ?>" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="user" class="icon-sm me-2"></i>
                    Personal Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               placeholder="e.g., Inspector Kondwani Banda" value="<?php echo e(old('name')); ?>">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" required
                               placeholder="e.g., <EMAIL>" value="<?php echo e(old('email')); ?>">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone" name="phone"
                               placeholder="e.g., +265 999 123 456" value="<?php echo e(old('phone')); ?>">
                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="badge_number" class="form-label">Badge Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="badge_number" name="badge_number" required
                               placeholder="e.g., PB001" value="<?php echo e(old('badge_number')); ?>">
                        <?php $__errorArgs = ['badge_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="profile_photo" class="form-label">Profile Photo</label>
                        <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                        <small class="text-muted">Upload a profile photo (JPG, PNG, max 2MB)</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="briefcase" class="icon-sm me-2"></i>
                    Professional Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="rank" class="form-label">Rank <span class="text-danger">*</span></label>
                        <select class="form-select" id="rank" name="rank" required>
                            <option value="">Select Rank</option>
                            <option value="Constable" <?php echo e(old('rank') == 'Constable' ? 'selected' : ''); ?>>Constable</option>
                            <option value="Senior Constable" <?php echo e(old('rank') == 'Senior Constable' ? 'selected' : ''); ?>>Senior Constable</option>
                            <option value="Sergeant" <?php echo e(old('rank') == 'Sergeant' ? 'selected' : ''); ?>>Sergeant</option>
                            <option value="Inspector" <?php echo e(old('rank') == 'Inspector' ? 'selected' : ''); ?>>Inspector</option>
                            <option value="Chief Inspector" <?php echo e(old('rank') == 'Chief Inspector' ? 'selected' : ''); ?>>Chief Inspector</option>
                            <option value="Superintendent" <?php echo e(old('rank') == 'Superintendent' ? 'selected' : ''); ?>>Superintendent</option>
                            <option value="Assistant Commissioner" <?php echo e(old('rank') == 'Assistant Commissioner' ? 'selected' : ''); ?>>Assistant Commissioner</option>
                            <option value="Deputy Commissioner" <?php echo e(old('rank') == 'Deputy Commissioner' ? 'selected' : ''); ?>>Deputy Commissioner</option>
                            <option value="Commissioner" <?php echo e(old('rank') == 'Commissioner' ? 'selected' : ''); ?>>Commissioner</option>
                        </select>
                        <?php $__errorArgs = ['rank'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                        <select class="form-select" id="department" name="department" required>
                            <option value="">Select Department</option>
                            <option value="Criminal Investigation" <?php echo e(old('department') == 'Criminal Investigation' ? 'selected' : ''); ?>>Criminal Investigation</option>
                            <option value="Traffic Police" <?php echo e(old('department') == 'Traffic Police' ? 'selected' : ''); ?>>Traffic Police</option>
                            <option value="Forensics" <?php echo e(old('department') == 'Forensics' ? 'selected' : ''); ?>>Forensics</option>
                            <option value="Administration" <?php echo e(old('department') == 'Administration' ? 'selected' : ''); ?>>Administration</option>
                            <option value="Community Policing" <?php echo e(old('department') == 'Community Policing' ? 'selected' : ''); ?>>Community Policing</option>
                            <option value="Special Operations" <?php echo e(old('department') == 'Special Operations' ? 'selected' : ''); ?>>Special Operations</option>
                            <option value="Intelligence" <?php echo e(old('department') == 'Intelligence' ? 'selected' : ''); ?>>Intelligence</option>
                        </select>
                        <?php $__errorArgs = ['department'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="station" class="form-label">Police Station</label>
                        <select class="form-select" id="station" name="station">
                            <option value="">Select Station</option>
                            <option value="Lilongwe Central" <?php echo e(old('station') == 'Lilongwe Central' ? 'selected' : ''); ?>>Lilongwe Central</option>
                            <option value="Blantyre Central" <?php echo e(old('station') == 'Blantyre Central' ? 'selected' : ''); ?>>Blantyre Central</option>
                            <option value="Mzuzu Central" <?php echo e(old('station') == 'Mzuzu Central' ? 'selected' : ''); ?>>Mzuzu Central</option>
                            <option value="Zomba Central" <?php echo e(old('station') == 'Zomba Central' ? 'selected' : ''); ?>>Zomba Central</option>
                            <option value="Kasungu" <?php echo e(old('station') == 'Kasungu' ? 'selected' : ''); ?>>Kasungu</option>
                            <option value="Mangochi" <?php echo e(old('station') == 'Mangochi' ? 'selected' : ''); ?>>Mangochi</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="supervisor" class="form-label">Supervisor</label>
                        <select class="form-select" id="supervisor" name="supervisor">
                            <option value="">Select Supervisor</option>
                            <option value="1">Commissioner Mwale</option>
                            <option value="2">Deputy Commissioner Banda</option>
                            <option value="3">Superintendent Phiri</option>
                            <option value="4">Chief Inspector Kumwenda</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="employment_date" class="form-label">Employment Date</label>
                        <input type="date" class="form-control" id="employment_date" name="employment_date"
                               value="<?php echo e(old('employment_date')); ?>">
                    </div>
                </div>
            </div>
        </div>

        <!-- System Access -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="shield" class="icon-sm me-2"></i>
                    System Access & Permissions
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="role" class="form-label">System Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="super_admin" <?php echo e(old('role') == 'super_admin' ? 'selected' : ''); ?>>Super Administrator</option>
                            <option value="admin" <?php echo e(old('role') == 'admin' ? 'selected' : ''); ?>>Administrator</option>
                            <option value="inspector" <?php echo e(old('role') == 'inspector' ? 'selected' : ''); ?>>Inspector</option>
                            <option value="officer" <?php echo e(old('role') == 'officer' ? 'selected' : ''); ?>>Officer</option>
                            <option value="forensic_analyst" <?php echo e(old('role') == 'forensic_analyst' ? 'selected' : ''); ?>>Forensic Analyst</option>
                            <option value="court_liaison" <?php echo e(old('role') == 'court_liaison' ? 'selected' : ''); ?>>Court Liaison</option>
                            <option value="viewer" <?php echo e(old('role') == 'viewer' ? 'selected' : ''); ?>>Viewer</option>
                        </select>
                        <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Account Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" <?php echo e(old('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(old('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="suspended" <?php echo e(old('status') == 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" required
                               placeholder="Enter secure password">
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger small"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required
                               placeholder="Confirm password">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="send_welcome_email" name="send_welcome_email" checked>
                            <label class="form-check-label" for="send_welcome_email">
                                Send welcome email with login credentials
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="force_password_change" name="force_password_change" checked>
                            <label class="form-check-label" for="force_password_change">
                                Force password change on first login
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i data-feather="info" class="icon-sm me-2"></i>
                    Additional Information
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="Additional notes about the user..."><?php echo e(old('notes')); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-outline-secondary">
                <i data-feather="x" class="icon-xs me-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-xs me-2"></i>
                Create User
            </button>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather Icons
    feather.replace();
    
    // Password strength indicator
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('password_confirmation');
    
    // Password confirmation validation
    confirmPasswordInput.addEventListener('input', function() {
        if (this.value !== passwordInput.value) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });
    
    passwordInput.addEventListener('input', function() {
        if (confirmPasswordInput.value && confirmPasswordInput.value !== this.value) {
            confirmPasswordInput.setCustomValidity('Passwords do not match');
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/users/create.blade.php ENDPATH**/ ?>