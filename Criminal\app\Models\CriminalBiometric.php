<?php

namespace App\Models;

use App\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CriminalBiometric extends Model implements HasMedia
{
    use HasFactory, HasUserTracking, SoftDeletes, LogsActivity, InteractsWithMedia;

    protected $fillable = [
        'criminal_id',
        'biometric_type',
        'file_path',
        'file_hash',
        'metadata',
        'collected_date',
        'collected_by',
        'verified',
        'description',
        'notes',
    ];

    protected $casts = [
        'metadata' => 'array',
        'collected_date' => 'date',
        'verified' => 'boolean',
    ];

    /**
     * Get the activity log options.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['biometric_type', 'verified'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the criminal that owns the biometric data.
     */
    public function criminal()
    {
        return $this->belongsTo(Criminal::class);
    }

    /**
     * Get the user who collected the biometric data.
     */
    public function collector()
    {
        return $this->belongsTo(User::class, 'collected_by');
    }

    /**
     * Scope for verified biometrics.
     */
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    /**
     * Scope for specific biometric type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('biometric_type', $type);
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('biometric_files')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'application/pdf']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10)
            ->performOnCollections('biometric_files');
    }
}
