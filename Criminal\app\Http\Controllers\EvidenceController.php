<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class EvidenceController extends Controller
{
    /**
     * Display a listing of the evidence.
     */
    public function index(Request $request)
    {
        // Mock data for evidence - in real application, this would come from database
        $evidenceData = collect([
            [
                'id' => 1,
                'evidence_id' => 'EV2024001',
                'description' => 'Knife used in robbery',
                'category' => 'Physical',
                'case_number' => '*********',
                'collection_date' => '2024-12-15',
                'collector' => 'Officer Banda',
                'storage_location' => 'Shelf A1, Room 3',
                'status' => 'Analyzed'
            ],
            [
                'id' => 2,
                'evidence_id' => 'EV2024002',
                'description' => 'CCTV footage from scene',
                'category' => 'Digital',
                'case_number' => '*********',
                'collection_date' => '2024-12-15',
                'collector' => 'Tech Officer Mwale',
                'storage_location' => 'Digital Archive Server 1',
                'status' => 'Under Analysis'
            ],
            [
                'id' => 3,
                'evidence_id' => '*********',
                'description' => 'Fingerprints from door handle',
                'category' => 'Biological',
                'case_number' => '*********',
                'collection_date' => '2024-12-12',
                'collector' => 'Forensic Officer Phiri',
                'storage_location' => 'Freezer Unit B2',
                'status' => 'Pending Analysis'
            ],
            [
                'id' => 4,
                'evidence_id' => '*********',
                'description' => 'Drug samples',
                'category' => 'Chemical',
                'case_number' => '*********',
                'collection_date' => '2024-12-12',
                'collector' => 'Officer Banda',
                'storage_location' => 'Secure Cabinet C1',
                'status' => 'Analyzed'
            ]
        ]);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $evidenceData = $evidenceData->filter(function ($evidence) use ($search) {
                return stripos($evidence['evidence_id'], $search) !== false ||
                       stripos($evidence['description'], $search) !== false ||
                       stripos($evidence['case_number'], $search) !== false;
            });
        }

        if ($request->filled('category')) {
            $evidenceData = $evidenceData->where('category', $request->get('category'));
        }

        if ($request->filled('status')) {
            $evidenceData = $evidenceData->where('status', $request->get('status'));
        }

        // Simulate pagination
        $perPage = 20;
        $currentPage = $request->get('page', 1);
        $total = $evidenceData->count();
        $evidence = $evidenceData->forPage($currentPage, $perPage);

        // Create a paginator instance
        $evidence = new \Illuminate\Pagination\LengthAwarePaginator(
            $evidence,
            $total,
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        return view('evidence.index', compact('evidence'));
    }

    /**
     * Show the form for creating a new evidence.
     */
    public function create()
    {
        return view('evidence.create');
    }

    /**
     * Store a newly created evidence in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'evidence_id' => 'required|string|max:255|unique:evidence,evidence_id',
            'case_number' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'description' => 'required|string',
            'collection_date' => 'required|date',
            'collector_name' => 'required|string|max:255',
            'storage_location' => 'required|string|max:255',
        ]);

        // For now, just redirect back with success message
        // In a real application, you would save to database here

        return redirect()->route('evidence.index')->with('success', 'Evidence item added successfully!');
    }

    /**
     * Display the specified evidence.
     */
    public function show(string $id)
    {
        return view('evidence.show', compact('id'));
    }

    /**
     * Show the form for editing the specified evidence.
     */
    public function edit(string $id)
    {
        return view('evidence.edit', compact('id'));
    }

    /**
     * Update the specified evidence in storage.
     */
    public function update(Request $request, string $id)
    {
        // Validate and update evidence
        return redirect()->route('evidence.index')->with('success', 'Evidence item updated successfully!');
    }

    /**
     * Remove the specified evidence from storage.
     */
    public function destroy(string $id)
    {
        return redirect()->route('evidence.index')->with('success', 'Evidence item deleted successfully!');
    }

    /**
     * Display chain of custody for evidence.
     */
    public function custody()
    {
        return view('evidence.custody');
    }

    /**
     * Display forensic analysis page.
     */
    public function forensic()
    {
        return view('evidence.forensic');
    }
}
