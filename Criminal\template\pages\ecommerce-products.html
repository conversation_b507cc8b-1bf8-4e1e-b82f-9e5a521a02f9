<!DOCTYPE html>
<html lang="en">
	
<!-- Mirrored from dashui.codescandy.com/dashuipro/pages/ecommerce-products.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:29:16 GMT -->
<head>
		<!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<meta name="author" content="Codescandy" />

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M8S4MT3EYG"></script>
<script>
	window.dataLayer = window.dataLayer || [];
	function gtag() {
		dataLayer.push(arguments);
	}
	gtag('js', new Date());

	gtag('config', 'G-M8S4MT3EYG');
</script>

<!-- Favicon icon-->
<link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon/favicon.ico" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
<link href="../assets/libs/%40mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

		<link href="../assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
		<title>Product List | Dash UI - Responsive Bootstrap 5 Admin Dashboard</title>
	</head>

	<body>
		<!-- Wrapper -->
		<main id="main-wrapper" class="main-wrapper">
			<div class="header">
	<!-- navbar -->
	<div class="navbar-custom navbar navbar-expand-lg">
		<div class="container-fluid px-0">
			<a class="navbar-brand d-block d-md-none" href="../index.html">
				<img src="../assets/images/brand/logo/logo-2.svg" alt="Image" />
			</a>

			<a id="nav-toggle" href="#!" class="ms-auto ms-md-0 me-0 me-lg-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-text-indent-left text-muted" viewBox="0 0 16 16">
					<path
						d="M2 3.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm.646 2.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L4.293 8 2.646 6.354a.5.5 0 0 1 0-.708zM7 6.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm-5 3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
					/>
				</svg>
			</a>

			<div class="d-none d-md-none d-lg-block">
				<!-- Form -->
				<form action="#">
					<div class="input-group">
						<input class="form-control rounded-3 bg-transparent ps-9" type="search" value="" id="searchInput" placeholder="Search" />
						<span class="">
							<button class="btn position-absolute start-0" type="button">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="feather feather-search text-dark"
								>
									<circle cx="11" cy="11" r="8"></circle>
									<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
								</svg>
							</button>
						</span>
					</div>
				</form>
			</div>
			<!--Navbar nav -->
			<ul class="navbar-nav navbar-right-wrap ms-lg-auto d-flex nav-top-wrap align-items-center ms-4 ms-lg-0">
				<li>
					<div class="dropdown">
						<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
							<i class="bi theme-icon-active"></i>
							<span class="visually-hidden bs-theme-text">Toggle theme</span>
						</button>
						<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
									<i class="bi theme-icon bi-sun-fill"></i>
									<span class="ms-2">Light</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
									<i class="bi theme-icon bi-moon-stars-fill"></i>
									<span class="ms-2">Dark</span>
								</button>
							</li>
							<li>
								<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
									<i class="bi theme-icon bi-circle-half"></i>
									<span class="ms-2">Auto</span>
								</button>
							</li>
						</ul>
					</div>
				</li>

				<li class="dropdown stopevent ms-2">
					<a class="btn btn-ghost btn-icon rounded-circle" href="#!" role="button" id="dropdownNotification" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="icon-xs" data-feather="bell"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end" aria-labelledby="dropdownNotification">
						<div>
							<div class="border-bottom px-3 pt-2 pb-3 d-flex justify-content-between align-items-center">
								<p class="mb-0 text-dark fw-medium fs-4">Notifications</p>
								<a href="#!" class="text-muted">
									<span>
										<i class="me-1 icon-xs" data-feather="settings"></i>
									</span>
								</a>
							</div>
							<div data-simplebar style="height: 250px">
								<!-- List group -->
								<ul class="list-group list-group-flush notification-list-scroll">
									<!-- List group item -->
									<li class="list-group-item bg-light">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Rishi Chopra</h5>
											<p class="mb-0">Mauris blandit erat id nunc blandit, ac eleifend dolor pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Neha Kannned</h5>
											<p class="mb-0">Proin at elit vel est condimentum elementum id in ante. Maecenas et sapien metus.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Nirmala Chauhan</h5>
											<p class="mb-0">Morbi maximus urna lobortis elit sollicitudin sollicitudieget elit vel pretium.</p>
										</a>
									</li>
									<!-- List group item -->
									<li class="list-group-item">
										<a href="#!" class="text-muted">
											<h5 class="mb-1">Sina Ray</h5>
											<p class="mb-0">Sed aliquam augue sit amet mauris volutpat hendrerit sed nunc eu diam.</p>
										</a>
									</li>
								</ul>
							</div>
							<div class="border-top px-3 py-2 text-center">
								<a href="#!" class="text-inherit">View all Notifications</a>
							</div>
						</div>
					</div>
				</li>
				<!-- List -->
				<li class="dropdown ms-2">
					<a class="rounded-circle" href="#!" role="button" id="dropdownUser" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<div class="avatar avatar-md avatar-indicators avatar-online">
							<img alt="avatar" src="../assets/images/avatar/avatar-11.jpg" class="rounded-circle" />
						</div>
					</a>
					<div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownUser">
						<div class="px-4 pb-0 pt-2">
							<div class="lh-1">
								<h5 class="mb-1">John E. Grainger</h5>
								<a href="#!" class="text-inherit fs-6">View my profile</a>
							</div>
							<div class="dropdown-divider mt-3 mb-2"></div>
						</div>

						<ul class="list-unstyled">
							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="user"></i>
									Edit Profile
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="activity"></i>
									Activity Log
								</a>
							</li>

							<li>
								<a class="dropdown-item d-flex align-items-center" href="#!">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="settings"></i>
									Settings
								</a>
							</li>
							<li>
								<a class="dropdown-item" href="../index.html">
									<i class="me-2 icon-xxs dropdown-item-icon" data-feather="power"></i>
									Sign Out
								</a>
							</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

			<!-- navbar vertical -->
			<!-- Sidebar -->

<div class="navbar-vertical navbar nav-dashboard">
	<div class="h-100" data-simplebar>
		<!-- Brand logo -->
		<a class="navbar-brand" href="../index.html">
			<img src="../assets/images/brand/logo/logo-2.svg" alt="dash ui - bootstrap 5 admin dashboard template" />
		</a>
		<!-- Navbar nav -->
		<ul class="navbar-nav flex-column" id="sideNavbar">
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navDashboard"
					aria-expanded="false"
					aria-controls="navDashboard"
				>
					<i data-feather="home" class="nav-icon me-2 icon-xxs"></i>
					Dashboard
				</a>

				<div id="navDashboard" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="dashboard-analytics.html">Analytics</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Project</a>
						</li>

						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-ecommerce.html">Ecommerce</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-crm.html">CRM</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-finance.html">Finance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="dashboard-blog.html">Blog</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Apps</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="calendar.html">Calendar</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow " href="chat-app.html">
					<i data-feather="message-square" class="nav-icon me-2 icon-xxs"></i>
					Chat
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navecommerce"
					aria-expanded="false"
					aria-controls="navecommerce"
				>
					<i data-feather="shopping-cart" class="nav-icon me-2 icon-xxs"></i>
					Ecommerce
				</a>

				<div id="navecommerce" class="collapse  show " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow  active " href="ecommerce-products.html">Products</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-products-details.html">Product Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-product-edit.html">Add Product</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-order-list.html">Orders</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-order-detail.html">Orders Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-cart.html">Shopping cart</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-checkout.html">Checkout</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-customer.html">Customer</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="ecommerce-seller.html">Seller</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navEmail" aria-expanded="false" aria-controls="navEmail">
					<i data-feather="mail" class="nav-icon me-2 icon-xxs"></i>
					Email
				</a>

				<div id="navEmail" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail.html">Inbox</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail-details.html">Details</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="mail-draft.html">Draft</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navKanban"
					aria-expanded="false"
					aria-controls="navKanban"
				>
					<i data-feather="layout" class="nav-icon me-2 icon-xxs"></i>
					Kanban
				</a>

				<div id="navKanban" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="task-kanban-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="task-kanban-list.html">List</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navProject" aria-expanded="false" aria-controls="navProject">
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Project
				</a>
				<div id="navProject" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="project-grid.html">Grid</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="project-list.html">List</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navprojectSingle"
								aria-expanded="false"
								aria-controls="navprojectSingle"
							>
								Single
							</a>
							<div id="navprojectSingle" class="collapse " data-bs-parent="#navProject">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="project-overview.html">Overview</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-task.html">Task</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-budget.html">Budget</a>
									</li>

									<li class="nav-item">
										<a class="nav-link " href="project-files.html">Files</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="project-team.html">Team</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="add-project.html">Create Project</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a class="nav-link " href="apps-file-manager.html">
					<i data-feather="folder-plus" class="nav-icon me-2 icon-xxs"></i>
					File Manager
				</a>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navCRM" aria-expanded="false" aria-controls="navCRM">
					<i data-feather="pie-chart" class="nav-icon me-2 icon-xxs"></i>

					CRM
				</a>

				<div id="navCRM" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="crm-contacts.html">Contacts</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="crm-company.html">Company</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="deals.html">
								Deals
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="deals-single.html">
								Deals Single
								<span class="badge text-bg-primary ms-2">New</span>
							</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navinvoice"
					aria-expanded="false"
					aria-controls="navinvoice"
				>
					<i data-feather="clipboard" class="nav-icon me-2 icon-xxs"></i>
					Invoice
				</a>

				<div id="navinvoice" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-list.html">List</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="invoice-generator.html">Invoice Generator</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navprofilePages"
					aria-expanded="false"
					aria-controls="navprofilePages"
				>
					<i data-feather="user" class="nav-icon me-2 icon-xxs"></i>
					Profile
				</a>
				<div id="navprofilePages" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="profile-overview.html">Overview</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-project.html">Project</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-files.html">Files</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="profile-team.html">Team</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-followers.html">Followers</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-activity.html">Activity</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="profile-settings.html">Settings</a>
						</li>
					</ul>
				</div>
			</li>
			<!-- Nav item -->
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navblog" aria-expanded="false" aria-controls="navblog">
					<i data-feather="edit" class="nav-icon me-2 icon-xxs"></i>
					Blog
				</a>

				<div id="navblog" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link has-arrow " href="blog-author.html">Author</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="blog-author-detail.html">Detail</a>
						</li>
						<li class="nav-item">
							<a class="nav-link has-arrow " href="create-blog-post.html">Create Post</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Layouts & Pages</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navlayoutPage"
					aria-expanded="false"
					aria-controls="navlayoutPage"
				>
					<i class="nav-icon me-2 icon-xxs" data-feather="file"></i>
					Pages
				</a>
				<div id="navlayoutPage" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="starter.html">Starter</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="pricing.html">Pricing</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="maintenance.html">Maintenance</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="404-error.html">404 Error</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navAuthentication"
					aria-expanded="false"
					aria-controls="navAuthentication"
				>
					<i data-feather="lock" class="nav-icon me-2 icon-xxs"></i>
					Authentication
				</a>
				<div id="navAuthentication" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="sign-in.html">Sign In</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="sign-up.html">Sign Up</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="forget-password.html">Forget Password</a>
						</li>
					</ul>
				</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navLayouts"
					aria-expanded="false"
					aria-controls="navLayouts"
				>
					<i data-feather="sidebar" class="nav-icon me-2 icon-xxs"></i>
					Layouts
				</a>
				<div id="navLayouts" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a class="nav-link " href="../index.html">Default</a>
						</li>

						<li class="nav-item">
							<a class="nav-link " href="../horizontal/index.html">Horizontal</a>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">UI Components</div>
			</li>
			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navComponents"
					aria-expanded="false"
					aria-controls="navComponents"
				>
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Components
				</a>
				<div id="navComponents" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a href="components/accordions.html" class="nav-link ">Accordions</a>
						</li>
						<li class="nav-item">
							<a class="nav-link " href="components/alerts.html">Alert</a>
						</li>

						<li class="nav-item">
							<a href="components/badge.html" class="nav-link ">Badge</a>
						</li>

						<li class="nav-item">
							<a href="components/breadcrumb.html" class="nav-link ">Breadcrumb</a>
						</li>
						<li class="nav-item">
							<a href="components/buttons.html" class="nav-link ">Buttons</a>
						</li>
						<li class="nav-item">
							<a href="components/button-group.html" class="nav-link ">Button group</a>
						</li>
						<li class="nav-item">
							<a href="components/card.html" class="nav-link ">Card</a>
						</li>
						<li class="nav-item">
							<a href="components/carousel.html" class="nav-link ">Carousel</a>
						</li>
						<li class="nav-item">
							<a href="components/close-button.html" class="nav-link ">Close Button</a>
						</li>
						<li class="nav-item">
							<a href="components/collapse.html" class="nav-link ">Collapse</a>
						</li>
						<li class="nav-item">
							<a href="components/dropdowns.html" class="nav-link ">Dropdowns</a>
						</li>
						<li class="nav-item">
							<a href="components/forms.html" class="nav-link ">Forms</a>
						</li>

						<li class="nav-item">
							<a href="components/list-group.html" class="nav-link ">List group</a>
						</li>

						<li class="nav-item">
							<a href="components/modal.html" class="nav-link ">Modal</a>
						</li>
						<li class="nav-item">
							<a href="components/navs-tabs.html" class="nav-link ">Navs and tabs</a>
						</li>
						<li class="nav-item">
							<a href="components/navbar.html" class="nav-link ">Navbar</a>
						</li>
						<li class="nav-item">
							<a href="components/offcanvas.html" class="nav-link ">Offcanvas</a>
						</li>
						<li class="nav-item">
							<a href="components/pagination.html" class="nav-link ">Pagination</a>
						</li>
						<li class="nav-item">
							<a href="components/placeholders.html" class="nav-link ">Placeholders</a>
						</li>
						<li class="nav-item">
							<a href="components/popovers.html" class="nav-link ">Popovers</a>
						</li>
						<li class="nav-item">
							<a href="components/progress.html" class="nav-link ">Progress</a>
						</li>
						<li class="nav-item">
							<a href="components/scrollspy.html" class="nav-link ">Scrollspy</a>
						</li>
						<li class="nav-item">
							<a href="components/spinners.html" class="nav-link ">Spinners</a>
						</li>
						<li class="nav-item">
							<a href="components/tables.html" class="nav-link ">Tables</a>
						</li>
						<li class="nav-item">
							<a href="components/toasts.html" class="nav-link ">Toasts</a>
						</li>
						<li class="nav-item">
							<a href="components/tooltips.html" class="nav-link ">Tooltips</a>
						</li>
					</ul>
				</div>
			</li>

			<li class="nav-item">
				<a
					class="nav-link has-arrow  collapsed "
					href="#!"
					data-bs-toggle="collapse"
					data-bs-target="#navMenuLevel"
					aria-expanded="false"
					aria-controls="navMenuLevel"
				>
					<i data-feather="corner-left-down" class="nav-icon me-2 icon-xxs"></i>
					Menu Level
				</a>
				<div id="navMenuLevel" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a
								class="nav-link has-arrow "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelSecond"
								aria-expanded="false"
								aria-controls="navMenuLevelSecond"
							>
								Two Level
							</a>
							<div id="navMenuLevelSecond" class="collapse" data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 1</a>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">NavItem 2</a>
									</li>
								</ul>
							</div>
						</li>
						<li class="nav-item">
							<a
								class="nav-link has-arrow  collapsed "
								href="#!"
								data-bs-toggle="collapse"
								data-bs-target="#navMenuLevelThree"
								aria-expanded="false"
								aria-controls="navMenuLevelThree"
							>
								Three Level
							</a>
							<div id="navMenuLevelThree" class="collapse " data-bs-parent="#navMenuLevel">
								<ul class="nav flex-column">
									<li class="nav-item">
										<a
											class="nav-link  collapsed "
											href="#!"
											data-bs-toggle="collapse"
											data-bs-target="#navMenuLevelThreeOne"
											aria-expanded="false"
											aria-controls="navMenuLevelThreeOne"
										>
											NavItem 1
										</a>
										<div id="navMenuLevelThreeOne" class="collapse collapse " data-bs-parent="#navMenuLevelThree">
											<ul class="nav flex-column">
												<li class="nav-item">
													<a class="nav-link " href="#!">NavChild Item 1</a>
												</li>
											</ul>
										</div>
									</li>
									<li class="nav-item">
										<a class="nav-link " href="#!">Nav Item 2</a>
									</li>
								</ul>
							</div>
						</li>
					</ul>
				</div>
			</li>

			<!-- Nav item -->
			<li class="nav-item">
				<div class="navbar-heading">Documentation</div>
			</li>
			<li class="nav-item">
				<a class="nav-link has-arrow  collapsed " href="#!" data-bs-toggle="collapse" data-bs-target="#navDocs" aria-expanded="false" aria-controls="navDocs">
					<i data-feather="package" class="nav-icon me-2 icon-xxs"></i>
					Docs
				</a>
				<div id="navDocs" class="collapse " data-bs-parent="#sideNavbar">
					<ul class="nav flex-column">
						<li class="nav-item"><a href="../docs/index.html" class="nav-link ">Introduction</a></li>
						<li class="nav-item"><a href="../docs/environment-setup.html" class="nav-link ">Environment setup</a></li>
						<li class="nav-item"><a href="../docs/working-with-gulp.html" class="nav-link ">Working with Gulp</a></li>
						<li class="nav-item"><a href="../docs/compiled-files.html" class="nav-link ">Compiled Files</a></li>
						<li class="nav-item"><a href="../docs/file-structure.html" class="nav-link ">File Structure</a></li>
						<li class="nav-item"><a href="../docs/resources-assets.html" class="nav-link ">Resources & assets</a></li>
						<li class="nav-item"><a href="../docs/changelog.html" class="nav-link ">Changelog</a></li>
					</ul>
				</div>
			</li>
		</ul>
		<div class="card bg-light shadow-none text-center mx-4 my-8">
			<div class="card-body py-6">
				<img src="../assets/images/background/giftbox.png" alt="dash ui - admin dashboard template" />
				<div class="mt-4">
					<h5>Unlimited Access</h5>
					<p class="fs-6 mb-4">Upgrade your plan from a Free trial, to select Business Plan. Start Now</p>
					<a href="#" class="btn btn-secondary btn-sm">Upgrade Now</a>
				</div>
			</div>
		</div>
	</div>
</div>


			<!-- Page Content -->

			<div id="app-content">
				<!-- Container fluid -->
				<div class="app-content-area">
					<div class="container-fluid">
						<div class="row">
							<div class="col-lg-12 col-md-12 col-12">
								<!-- Page header -->
								<div class="mb-5">
									<h3 class="mb-0">Products</h3>
								</div>
							</div>
						</div>
						<div>
							<!-- row -->
							<div class="row">
								<div class="col-12">
									<div class="card">
										<div class="card-header d-md-flex border-bottom-0">
											<div class="flex-grow-1">
												<a href="#!" class="btn btn-primary">+ Add Product</a>
											</div>
											<div class="mt-3 mt-md-0">
												<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="settingOne">
													<i data-feather="settings" class="icon-xs"></i>
													<div id="settingOne" class="d-none">
														<span>Setting</span>
													</div>
												</a>

												<a href="#!" class="btn btn-outline-white ms-2">Import</a>
												<a href="#!" class="btn btn-outline-white ms-2">Export</a>
											</div>
										</div>
										<div class="card-body">
											<div class="table-responsive table-card">
												<table id="example" class="table text-nowrap table-centered mt-0" style="width: 100%">
													<thead class="table-light">
														<tr>
															<th class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="checkAll" />
																	<label class="form-check-label" for="checkAll"></label>
																</div>
															</th>
															<th class="ps-1">Product</th>
															<th>Category</th>
															<th>Added Date</th>
															<th>Price</th>
															<th>Quantity</th>
															<th>Status</th>
															<th>Action</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox2" />
																	<label class="form-check-label" for="contactCheckbox2"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-1.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Women Shoes</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Accessories</td>
															<td>19 July, 2023</td>
															<td>$65.29</td>
															<td>235</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeOne">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeOne" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editOne">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editOne" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashOne">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashOne" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox3" />
																	<label class="form-check-label" for="contactCheckbox3"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-2.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Black Round Sunglasses</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Bags</td>
															<td>19 July, 2023</td>
															<td>$15.99</td>
															<td>56</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeTwo">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeTwo" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="edit">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="edit" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashTwo">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashTwo" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox4" />
																	<label class="form-check-label" for="contactCheckbox4"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-8.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Black Round Sunglasses</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Men's Fashion</td>
															<td>19 July, 2023</td>
															<td>$12.39</td>
															<td>67</td>
															<td>
																<span class="badge badge-danger-soft">Deactive</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeThree">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeThree" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editTwo">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editTwo" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashThree">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashThree" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox5" />
																	<label class="form-check-label" for="contactCheckbox5"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-9.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Dimond Earning</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Women's Fashion</td>
															<td>18 July, 2023</td>
															<td>$35.99</td>
															<td>24</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeFour">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeFour" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editThree">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editThree" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashFour">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashFour" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox6" />
																	<label class="form-check-label" for="contactCheckbox6"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-3.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Shoulder Bag</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Accessories</td>
															<td>17 July, 2023</td>
															<td>$65.29</td>
															<td>32</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeFive">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeFive" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editFour">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editFour" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashFive">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashFive" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox16" />
																	<label class="form-check-label" for="contactCheckbox16"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-4.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Vinage Perfume</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Bags</td>
															<td>20 July, 2023</td>
															<td>$45.29</td>
															<td>45</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeSix">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeSix" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editFive">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editFive" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashSix">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashSix" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox7" />
																	<label class="form-check-label" for="contactCheckbox7"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-5.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Apple airpods</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Men's Fashion</td>
															<td>20 July, 2023</td>
															<td>$65.29</td>
															<td>45</td>
															<td>
																<span class="badge badge-danger-soft">Deactive</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeSeven">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeSeven" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editSix">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editSix" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashSeven">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashSeven" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox8" />
																	<label class="form-check-label" for="contactCheckbox8"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-6.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Sandy Perfume</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Women's Fashion</td>
															<td>18 July, 2023</td>
															<td>$35.29</td>
															<td>34</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeNine">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeNine" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editSeven">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editSeven" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashEight">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashEight" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox9" />
																	<label class="form-check-label" for="contactCheckbox9"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-7.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Man Wallet</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Bags</td>
															<td>19 July, 2023</td>
															<td>$15.99</td>
															<td>56</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeEleven">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeEleven" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editEight">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editEight" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashNine">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashNine" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox10" />
																	<label class="form-check-label" for="contactCheckbox10"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-8.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Black Rectangle Sunglasses</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Men's Fashion</td>
															<td>19 July, 2023</td>
															<td>$12.39</td>
															<td>67</td>
															<td>
																<span class="badge badge-danger-soft">Deactive</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeTweleve">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeTweleve" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editNine">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editNine" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashTen">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashTen" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox11" />
																	<label class="form-check-label" for="contactCheckbox11"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-9.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Dimond Earning</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Women's Fashion</td>
															<td>18 July, 2023</td>
															<td>$35.99</td>
															<td>24</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeThirteen">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeThirteen" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editTen">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editTen" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashEleven">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashEleven" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox12" />
																	<label class="form-check-label" for="contactCheckbox12"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-2.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Black Round Sunglasses</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Accessories</td>
															<td>17 July, 2023</td>
															<td>$65.29</td>
															<td>32</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeFourteen">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeFourteen" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editEleven">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editEleven" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashTwelve">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashTwelve" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox13" />
																	<label class="form-check-label" for="contactCheckbox13"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-3.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Shoulder Bag</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Bags</td>
															<td>20 July, 2023</td>
															<td>$45.29</td>
															<td>45</td>
															<td>
																<span class="badge badge-success-soft">Active</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeFifteen">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeFifteen" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editThirteen">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editThirteen" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashThirteen">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashThirteen" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
														<tr>
															<td class="pe-0">
																<div class="form-check">
																	<input class="form-check-input" type="checkbox" value="" id="contactCheckbox14" />
																	<label class="form-check-label" for="contactCheckbox14"></label>
																</div>
															</td>
															<td class="ps-0">
																<div class="d-flex align-items-center">
																	<img src="../assets/images/ecommerce/product-4.jpg" alt="" class="img-4by3-sm rounded-3" />
																	<div class="ms-3">
																		<h5 class="mb-0">
																			<a href="#!" class="text-inherit">Vintage Perfume</a>
																		</h5>
																		<span class="text-warning">
																			<i class="mdi mdi-star"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																			<i class="mdi mdi-star ms-n1"></i>
																		</span>
																	</div>
																</div>
															</td>
															<td>Men's Fashion</td>
															<td>20 July, 2023</td>
															<td>$65.29</td>
															<td>45</td>
															<td>
																<span class="badge badge-danger-soft">Deactive</span>
															</td>
															<td>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="eyeSixteen">
																	<i data-feather="eye" class="icon-xs"></i>
																	<div id="eyeSixteen" class="d-none">
																		<span>View</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="editFourteen">
																	<i data-feather="edit" class="icon-xs"></i>
																	<div id="editFourteen" class="d-none">
																		<span>Edit</span>
																	</div>
																</a>
																<a href="#!" class="btn btn-ghost btn-icon btn-sm rounded-circle texttooltip" data-template="trashFourteen">
																	<i data-feather="trash-2" class="icon-xs"></i>
																	<div id="trashFourteen" class="d-none">
																		<span>Delete</span>
																	</div>
																</a>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</main>

		<!-- Modal -->
		<div class="modal fade" id="addNewContactModal" tabindex="-1" aria-labelledby="addNewContactModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="addNewContactModalLabel">Add New Contact</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div>
							<div class="d-flex align-items-center">
								<img src="../assets/images/avatar/avatar-11.jpg" alt="Image" class="avatar avatar-lg rounded-circle" />
								<div class="ms-3">
									<a href="javascript:void(0);" class="btn btn-outline-white">Upload Photo</a>
								</div>
							</div>
							<div class="mt-5">
								<form>
									<div class="mb-3">
										<label class="form-label" for="fname">Name</label>
										<input type="text" class="form-control" placeholder="Enter Name" id="fname" />
									</div>
									<div class="mb-3">
										<label class="form-label" for="email">Email</label>
										<input type="email" class="form-control" placeholder="Enter Email" id="email" />
									</div>
									<div class="mb-3">
										<label class="form-label" for="phone">Phone Number</label>
										<input type="text" class="form-control" placeholder="Enter Phone" id="phone" />
									</div>
									<div class="mb-3">
										<label class="form-label" for="companyName">Company</label>
										<input type="text" class="form-control" placeholder="Enter Company Name" id="companyName" />
									</div>
									<div class="mb-3">
										<label class="form-label" for="designation">Designation</label>
										<input type="text" class="form-control" placeholder="Enter Designation" id="designation" />
									</div>
									<div class="mb-3">
										<label class="form-label" for="status">Lead Status</label>
										<input type="text" class="form-control" placeholder="Status" id="status" />
									</div>
									<div class="d-flex justify-content-end">
										<a href="#!" class="btn btn-primary">+ Add Customer</a>
										<a href="#!" class="btn btn-light ms-2">Close</a>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Scripts -->

		<!-- Libs JS -->

<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/feather-icons/dist/feather.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

		<!-- popper js -->
		<script src="../assets/libs/%40popperjs/core/dist/umd/popper.min.js"></script>
		<!-- tippy js -->
		<script src="../assets/libs/tippy.js/dist/tippy-bundle.umd.min.js"></script>
		<script src="../assets/js/vendors/tooltip.js"></script>
		<script src="../assets/libs/jquery/dist/jquery.min.js"></script>
		<script src="../assets/libs/datatables/media/js/jquery.dataTables.min.js"></script>
		<script src="../assets/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
		<script src="../assets/libs/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
		<script src="../assets/js/vendors/datatable.js"></script>
	</body>

<!-- Mirrored from dashui.codescandy.com/dashuipro/pages/ecommerce-products.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 27 May 2025 08:29:23 GMT -->
</html>
