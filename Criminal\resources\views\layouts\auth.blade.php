<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="author" content="{{ config('app.name') }}" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/images/favicon/favicon.ico') }}" />

    <!-- Color modes -->
    <script src="{{ asset('assets/js/vendors/color-modes.js') }}"></script>

    <!-- Libs CSS -->
    <link href="{{ asset('assets/libs/bootstrap-icons/font/bootstrap-icons.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/libs/@mdi/font/css/materialdesignicons.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/libs/simplebar/dist/simplebar.min.css') }}" rel="stylesheet" />

    <!-- Theme CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">

    <title>@yield('title', config('app.name') . ' - Authentication')</title>

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Custom Branding Styles -->
    <style>
        .malawi-flag {
            background: linear-gradient(to bottom, #000 33%, #dc2626 33% 66%, #16a34a 66%);
            border-radius: 4px;
            display: inline-block;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
    </style>
</head>

<body class="bg-light">
    <main>
        <section class="container d-flex flex-column vh-100">
            <div class="row align-items-center justify-content-center g-0 h-lg-100 py-8">
                <div class="col-lg-5 col-md-8 py-8 py-xl-0">
                    <!-- Card -->
                    <div class="card shadow">
                        <div class="card-body p-6">
                            <div class="mb-4">
                                <div class="text-center">
                                    <a href="{{ route('dashboard') }}">
                                        <div class="d-flex align-items-center justify-content-center mb-4">
                                            <div class="malawi-flag me-3" style="width: 48px; height: 32px;"></div>
                                            <div class="text-center">
                                                <i data-feather="shield" style="width: 48px; height: 48px; color: #1e40af;"></i>
                                            </div>
                                        </div>
                                    </a>
                                    <h1 class="mb-1 fw-bold">{{ app_initials() }}</h1>
                                    <p class="text-muted small">{{ config('app.name') }}</p>
                                    <span class="text-muted">Malawi Law Enforcement</span>
                                </div>
                            </div>

                            <!-- Flash Messages -->
                            @if (session('status'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('status') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if ($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <!-- Content -->
                            {{ $slot }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <img src="{{ asset('images/company-logos/logo-no-bg.png') }}" alt="Gluhen Investment" class="me-2" style="width: 20px; height: 20px; object-fit: contain;">
                            <span class="text-muted small">
                                Powered by
                                <a href="https://gluheninvestment.com" target="_blank" class="text-decoration-none text-primary fw-medium">
                                    Gluhen Investment
                                </a>
                            </span>
                        </div>
                        <p class="text-muted small mb-0">&copy; {{ date('Y') }} Gluhen Investment. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Scripts -->
    <script src="{{ asset('assets/libs/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/dist/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- Page specific scripts -->
    @stack('scripts')

    <!-- Initialize Feather Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            feather.replace();
        });
    </script>

    <!-- Livewire Scripts -->
    @livewireScripts
</body>
</html>
