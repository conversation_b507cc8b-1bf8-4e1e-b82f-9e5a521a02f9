!function(){"use strict";function n(t,e){for(;t&&!t.classList.contains(e);)t=t.parentNode;return t}document.querySelectorAll(".input-group").forEach(function(t){t.addEventListener("click",function(t){var e,a;t.target.classList.contains("button-plus")?((e=t).preventDefault(),a=e.target.getAttribute("data-field"),e=n(e.target,"input-group").querySelector(`input[name='${a}']`),a=parseInt(e.value,10),isNaN(a)?e.value=0:e.value=a+1):t.target.classList.contains("button-minus")&&((e=t).preventDefault(),a=e.target.getAttribute("data-field"),e=n(e.target,"input-group").querySelector(`input[name='${a}']`),a=parseInt(e.value,10),!isNaN(a)&&0<a?e.value=a-1:e.value=0)})})}();