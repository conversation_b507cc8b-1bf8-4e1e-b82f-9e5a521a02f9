<?php if (isset($component)) { $__componentOriginal2794fdd104a4eebb18042e6478dc49e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2794fdd104a4eebb18042e6478dc49e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.criminal-form','data' => ['action' => route('criminals.store'),'title' => 'Add New Criminal Profile','breadcrumbs' => [
        ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
        ['title' => 'Add New Criminal']
    ],'backUrl' => route('criminals.index'),'submitText' => 'Save Criminal Profile']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('criminal-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('criminals.store')),'title' => 'Add New Criminal Profile','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Criminal Profiles', 'url' => route('criminals.index')],
        ['title' => 'Add New Criminal']
    ]),'back-url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('criminals.index')),'submit-text' => 'Save Criminal Profile']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2794fdd104a4eebb18042e6478dc49e6)): ?>
<?php $attributes = $__attributesOriginal2794fdd104a4eebb18042e6478dc49e6; ?>
<?php unset($__attributesOriginal2794fdd104a4eebb18042e6478dc49e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2794fdd104a4eebb18042e6478dc49e6)): ?>
<?php $component = $__componentOriginal2794fdd104a4eebb18042e6478dc49e6; ?>
<?php unset($__componentOriginal2794fdd104a4eebb18042e6478dc49e6); ?>
<?php endif; ?>

<?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/criminals/create.blade.php ENDPATH**/ ?>