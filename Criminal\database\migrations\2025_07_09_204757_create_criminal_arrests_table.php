<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('criminal_arrests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('criminal_id')->constrained('criminals')->onDelete('cascade');

            // Arrest Details
            $table->date('arrest_date');
            $table->time('arrest_time')->nullable();
            $table->text('arrest_location')->nullable();
            $table->string('arrest_report_number', 50)->nullable();

            // Officer Information
            $table->foreignId('arresting_officer_id')->nullable()->constrained('users')->onDelete('set null');

            // Charges and Reasons
            $table->text('arrest_reason')->nullable();
            $table->text('charges')->nullable();
            $table->text('other_offenses')->nullable();

            // Status
            $table->enum('status', ['Active', 'Released', 'Transferred', 'Bailed'])->default('Active');
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // User tracking fields
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes
            $table->index(['criminal_id']);
            $table->index(['arrest_date']);
            $table->index(['arresting_officer_id']);
            $table->index(['status']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('criminal_arrests');
    }
};
