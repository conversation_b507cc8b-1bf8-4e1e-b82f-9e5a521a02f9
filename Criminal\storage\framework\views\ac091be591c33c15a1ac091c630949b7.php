<?php $__env->startSection('title', 'Case Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Cases</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Case Management</h1>
        <a href="<?php echo e(route('cases.create')); ?>" class="btn btn-primary">
            <i data-feather="plus" class="icon-sm me-2"></i>
            Add New Case
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="text-white"><?php echo e($cases->total()); ?></h4>
                    <p class="mb-0">Total Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="text-white"><?php echo e($cases->where('status', 'Under Investigation')->count()); ?></h4>
                    <p class="mb-0">Under Investigation</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="text-white"><?php echo e($cases->where('status', 'Case Closed')->count()); ?></h4>
                    <p class="mb-0">Closed Cases</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 class="text-white"><?php echo e($cases->where('priority', 'High')->count() + $cases->where('priority', 'Urgent')->count()); ?></h4>
                    <p class="mb-0">High Priority</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i data-feather="filter" class="icon-sm me-2"></i>
                    Filters
                </h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="true">
                    <i data-feather="chevron-up" class="icon-xs"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="filtersCollapse">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('cases.index')); ?>" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" 
                               placeholder="Case number, title, complainant...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="Reported" <?php echo e(request('status') == 'Reported' ? 'selected' : ''); ?>>Reported</option>
                            <option value="Under Investigation" <?php echo e(request('status') == 'Under Investigation' ? 'selected' : ''); ?>>Under Investigation</option>
                            <option value="Pending Evidence" <?php echo e(request('status') == 'Pending Evidence' ? 'selected' : ''); ?>>Pending Evidence</option>
                            <option value="Pending Arrest" <?php echo e(request('status') == 'Pending Arrest' ? 'selected' : ''); ?>>Pending Arrest</option>
                            <option value="Suspect Arrested" <?php echo e(request('status') == 'Suspect Arrested' ? 'selected' : ''); ?>>Suspect Arrested</option>
                            <option value="Case Closed" <?php echo e(request('status') == 'Case Closed' ? 'selected' : ''); ?>>Case Closed</option>
                            <option value="Transferred" <?php echo e(request('status') == 'Transferred' ? 'selected' : ''); ?>>Transferred</option>
                            <option value="Cold Case" <?php echo e(request('status') == 'Cold Case' ? 'selected' : ''); ?>>Cold Case</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <option value="Low" <?php echo e(request('priority') == 'Low' ? 'selected' : ''); ?>>Low</option>
                            <option value="Medium" <?php echo e(request('priority') == 'Medium' ? 'selected' : ''); ?>>Medium</option>
                            <option value="High" <?php echo e(request('priority') == 'High' ? 'selected' : ''); ?>>High</option>
                            <option value="Urgent" <?php echo e(request('priority') == 'Urgent' ? 'selected' : ''); ?>>Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="Theft" <?php echo e(request('type') == 'Theft' ? 'selected' : ''); ?>>Theft</option>
                            <option value="Robbery" <?php echo e(request('type') == 'Robbery' ? 'selected' : ''); ?>>Robbery</option>
                            <option value="Burglary" <?php echo e(request('type') == 'Burglary' ? 'selected' : ''); ?>>Burglary</option>
                            <option value="Assault" <?php echo e(request('type') == 'Assault' ? 'selected' : ''); ?>>Assault</option>
                            <option value="Murder" <?php echo e(request('type') == 'Murder' ? 'selected' : ''); ?>>Murder</option>
                            <option value="Rape" <?php echo e(request('type') == 'Rape' ? 'selected' : ''); ?>>Rape</option>
                            <option value="Drug Offense" <?php echo e(request('type') == 'Drug Offense' ? 'selected' : ''); ?>>Drug Offense</option>
                            <option value="Fraud" <?php echo e(request('type') == 'Fraud' ? 'selected' : ''); ?>>Fraud</option>
                            <option value="Embezzlement" <?php echo e(request('type') == 'Embezzlement' ? 'selected' : ''); ?>>Embezzlement</option>
                            <option value="Domestic Violence" <?php echo e(request('type') == 'Domestic Violence' ? 'selected' : ''); ?>>Domestic Violence</option>
                            <option value="Traffic Offense" <?php echo e(request('type') == 'Traffic Offense' ? 'selected' : ''); ?>>Traffic Offense</option>
                            <option value="Cybercrime" <?php echo e(request('type') == 'Cybercrime' ? 'selected' : ''); ?>>Cybercrime</option>
                            <option value="Corruption" <?php echo e(request('type') == 'Corruption' ? 'selected' : ''); ?>>Corruption</option>
                            <option value="Other" <?php echo e(request('type') == 'Other' ? 'selected' : ''); ?>>Other</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="district" class="form-label">District</label>
                        <input type="text" class="form-control" id="district" name="district" 
                               value="<?php echo e(request('district')); ?>" 
                               placeholder="District">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i data-feather="search" class="icon-xs"></i>
                            </button>
                        </div>
                    </div>
                    <?php if(request()->hasAny(['search', 'status', 'priority', 'type', 'district', 'officer'])): ?>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="<?php echo e(route('cases.index')); ?>" class="btn btn-outline-secondary">
                                <i data-feather="x" class="icon-xs"></i>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <!-- Cases Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Cases List</h5>
        </div>
        <div class="card-body p-0">
            <?php if($cases->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Case Number</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Investigating Officer</th>
                            <th>Reported Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $cases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $case): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <a href="<?php echo e(route('cases.show', $case)); ?>" class="text-decoration-none">
                                    <?php echo e($case->case_number); ?>

                                </a>
                            </td>
                            <td>
                                <div class="fw-medium"><?php echo e(Str::limit($case->title, 40)); ?></div>
                                <small class="text-muted"><?php echo e($case->complainant_name); ?></small>
                            </td>
                            <td><?php echo e($case->type); ?></td>
                            <td>
                                <span class="badge <?php echo e($case->priority_badge_class); ?>">
                                    <?php echo e($case->priority); ?>

                                </span>
                            </td>
                            <td>
                                <span class="badge <?php echo e($case->status_badge_class); ?>">
                                    <?php echo e($case->status); ?>

                                </span>
                            </td>
                            <td>
                                <?php echo e($case->investigatingOfficer->name ?? 'Not Assigned'); ?>

                            </td>
                            <td><?php echo e($case->reported_date->format('M d, Y')); ?></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('cases.show', $case)); ?>">
                                            <i data-feather="eye" class="icon-xs me-2"></i>View
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('cases.edit', $case)); ?>">
                                            <i data-feather="edit" class="icon-xs me-2"></i>Edit
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="<?php echo e(route('cases.destroy', $case)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="dropdown-item text-danger" 
                                                        onclick="return confirm('Are you sure you want to delete this case?')">
                                                    <i data-feather="trash-2" class="icon-xs me-2"></i>Delete
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i data-feather="folder" class="icon-lg text-muted mb-3"></i>
                <h5 class="text-muted">No cases found</h5>
                <p class="text-muted">No cases match your current filters.</p>
                <a href="<?php echo e(route('cases.create')); ?>" class="btn btn-primary">
                    <i data-feather="plus" class="icon-sm me-2"></i>
                    Add First Case
                </a>
            </div>
            <?php endif; ?>
        </div>
        <?php if($cases->hasPages()): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing <?php echo e($cases->firstItem()); ?> to <?php echo e($cases->lastItem()); ?> of <?php echo e($cases->total()); ?> results
                </div>
                <?php echo e($cases->links('pagination::bootstrap-4')); ?>

            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Initialize Feather icons
    feather.replace();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\samy\Criminal\resources\views/cases/index.blade.php ENDPATH**/ ?>