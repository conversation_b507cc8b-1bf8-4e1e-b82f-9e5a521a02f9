<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('criminal_relationships', function (Blueprint $table) {
            $table->id();
            $table->foreignId('criminal_id')->constrained('criminals')->onDelete('cascade');

            // Relationship Information
            $table->string('related_person_name', 200);
            $table->enum('relationship_type', ['Family', 'Associate', 'Gang', 'Witness', 'Victim']);
            $table->string('relationship_detail', 100)->nullable();
            $table->string('contact_info', 200)->nullable();

            // Additional Information
            $table->text('notes')->nullable();
            $table->boolean('verified')->default(false);
            $table->date('last_contact')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // User tracking fields
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes
            $table->index(['criminal_id']);
            $table->index(['relationship_type']);
            $table->index(['verified']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('criminal_relationships');
    }
};
