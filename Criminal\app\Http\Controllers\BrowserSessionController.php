<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class BrowserSessionController extends Controller
{
    /**
     * Log out from other browser sessions.
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        if (!Hash::check($request->password, $request->user()->password)) {
            throw ValidationException::withMessages([
                'password' => [__('This password does not match our records.')],
            ]);
        }

        $this->logoutOtherBrowserSessions($request->password);

        return back()->with('status', 'Other browser sessions have been logged out successfully.');
    }

    /**
     * Log out from other browser sessions.
     */
    protected function logoutOtherBrowserSessions($password)
    {
        if (config('session.driver') !== 'database') {
            return;
        }

        Auth::guard()->logoutOtherDevices($password);

        $this->deleteOtherSessionRecords();
    }

    /**
     * Delete the other browser session records from storage.
     */
    protected function deleteOtherSessionRecords()
    {
        if (config('session.driver') !== 'database') {
            return;
        }

        DB::connection(config('session.connection'))->table(config('session.table', 'sessions'))
            ->where('user_id', Auth::user()->getAuthIdentifier())
            ->where('id', '!=', request()->session()->getId())
            ->delete();
    }
}
